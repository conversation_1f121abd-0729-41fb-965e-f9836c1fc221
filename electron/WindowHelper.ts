// electron/WindowHelper.ts

import { BrowserWindow, screen } from "electron";
import path from "node:path";
import { AppState } from "./main";
import { store } from "./store";

const isDev = process.env.NODE_ENV === "development"
const isMac = process.platform === "darwin"

const startUrl = isDev
  ? "http://localhost:5173"
  : `file://${path.join(__dirname, "../dist/index.html")}`

export class WindowHelper {
  private mainWindow: BrowserWindow | null = null
  private isWindowVisible: boolean = false
  private windowPosition: { x: number; y: number } | null = null
  private windowSize: { width: number; height: number } | null = null
  private appState: AppState

  // Initialize with explicit number type and 0 value
  private screenWidth: number = 0
  private screenHeight: number = 0
  private step: number = 0
  private currentX: number = 0
  private currentY: number = 0

  // Add this property to track focus
  private wasFocused: boolean = false

  // 保存用户设置的透明度
  private userOpacity: number = 1

  constructor(appState: AppState) {
    this.appState = appState
  }

  public setWindowDimensions(width: number, height: number): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return

    // Get current window position
    const [currentX, currentY] = this.mainWindow.getPosition()

    // Get screen dimensions
    const primaryDisplay = screen.getPrimaryDisplay()
    const workArea = primaryDisplay.workAreaSize
    const scaleFactor = primaryDisplay.scaleFactor || 1
    const isWindows = process.platform === 'win32'

    // 使用更合理的最大宽度限制 - 不超过屏幕宽度的65%（适中的语音面板宽度）
    const maxAllowedWidth = Math.floor(workArea.width * 0.65)

    // 减少额外填充，只添加必要的边距
    let extraPadding = 16
    if (isWindows && scaleFactor > 1) {
      // 在高DPI下适当增加填充，但不要过多
      extraPadding = Math.ceil(16 * scaleFactor)
    }

    // 使用实际内容宽度，只在必要时限制最大宽度
    const newWidth = Math.min(width + extraPadding, maxAllowedWidth)
    const newHeight = Math.ceil(height)

    console.log(`Content dimensions: ${width}x${height}, Final window size: ${newWidth}x${newHeight}, extraPadding: ${extraPadding}`)

    // 只有当窗口完全超出屏幕边界时才调整位置
    let newX = currentX
    
    // 检查窗口是否完全超出屏幕边界
    if (currentX + newWidth <= 0) {
      // 窗口完全在左边外，移到左边缘
      newX = 0
    } else if (currentX >= workArea.width) {
      // 窗口完全在右边外，移到右边缘
      newX = workArea.width - newWidth
    }
    // 如果窗口部分可见，保持当前位置不变

    // Update window bounds
    this.mainWindow.setBounds({
      x: newX,
      y: currentY,
      width: newWidth,
      height: newHeight
    })

    // Update internal state
    this.windowPosition = { x: newX, y: currentY }
    this.windowSize = { width: newWidth, height: newHeight }
    this.currentX = newX
  }

  public createWindow(): void {
    if (this.mainWindow !== null) return

    const primaryDisplay = screen.getPrimaryDisplay()
    const workArea = primaryDisplay.workAreaSize
    this.screenWidth = workArea.width
    this.screenHeight = workArea.height

    this.step = Math.floor(this.screenWidth / 50) // 50 steps
    this.currentX = 0 // Start at the left

    const windowSettings: Electron.BrowserWindowConstructorOptions = {
      height: 600,
      minWidth: undefined,
      maxWidth: undefined,
      x: this.currentX,
      y: 0,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: true,
        preload: path.join(__dirname, "preload.js")
      },
      show: true,
      type: 'panel',
      frame: false,
      transparent: true,
      fullscreenable: false,
      hasShadow: false,
      backgroundColor: "#00000000",
      focusable: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      enableLargerThanScreen: true,
      movable: true,
      // 添加以下设置以禁用滚动条
      useContentSize: true,  // 使用内容尺寸而不是窗口尺寸
      resizable: false       // 禁用窗口调整大小
    }

    this.mainWindow = new BrowserWindow(windowSettings)

    this.mainWindow.setContentProtection(true)

    // Check if API key exists in store
    const apiKey = store.get('openaiApiKey')
    if (!apiKey) {
      this.mainWindow.setIgnoreMouseEvents(false)
    } else {
      this.mainWindow.setIgnoreMouseEvents(true)
    }

    // Only call macOS specific methods if running on macOS
    if (isMac) {
      this.mainWindow.setHiddenInMissionControl(true)
      this.mainWindow.setVisibleOnAllWorkspaces(true, {
        visibleOnFullScreen: true
      })
    }

    this.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)

    this.mainWindow.loadURL(startUrl).catch((err) => {
      console.error("Failed to load URL:", err)
    })

    const bounds = this.mainWindow.getBounds()
    this.windowPosition = { x: bounds.x, y: bounds.y }
    this.windowSize = { width: bounds.width, height: bounds.height }
    this.currentX = bounds.x
    this.currentY = bounds.y

    this.setupWindowListeners()
    this.isWindowVisible = true

    // 初始化用户透明度为当前窗口透明度
    this.userOpacity = this.mainWindow.getOpacity()

    // Open DevTools if in development mode
    if (isDev) {
      // this.mainWindow.webContents.openDevTools();
    }
    
  }

  private setupWindowListeners(): void {
    if (!this.mainWindow) return

    this.mainWindow.on("move", () => {
      if (this.mainWindow) {
        const bounds = this.mainWindow.getBounds()
        this.windowPosition = { x: bounds.x, y: bounds.y }
        this.currentX = bounds.x
        this.currentY = bounds.y
      }
    })

    this.mainWindow.on("resize", () => {
      if (this.mainWindow) {
        const bounds = this.mainWindow.getBounds()
        this.windowSize = { width: bounds.width, height: bounds.height }
      }
    })

    this.mainWindow.on("closed", () => {
      this.mainWindow = null
      this.isWindowVisible = false
      this.windowPosition = null
      this.windowSize = null
    })
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  public isVisible(): boolean {
    return this.isWindowVisible
  }

  public hideMainWindow(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      console.warn("Main window does not exist or is destroyed.")
      return
    }

    // Store focus state before hiding
    this.wasFocused = this.mainWindow.isFocused()

    const bounds = this.mainWindow.getBounds()
    this.windowPosition = { x: bounds.x, y: bounds.y }
    this.windowSize = { width: bounds.width, height: bounds.height }
    
    // Hide the window
    this.mainWindow.hide()
    this.isWindowVisible = false
  }

  /**
   * Set whether the window should ignore mouse events
   * @param value - True to ignore mouse events, false to handle them
   */
  public setIgnoreMouseEvents(value: boolean): void {
    const mainWindow = this.getMainWindow();
    if (mainWindow) {
      mainWindow.setIgnoreMouseEvents(value);
    }
  }

  public showMainWindow(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      console.warn("Main window does not exist or is destroyed.")
      return
    }

    const focusedWindow = BrowserWindow.getFocusedWindow()

    if (this.windowPosition && this.windowSize) {
      this.mainWindow.setBounds({
        x: this.windowPosition.x,
        y: this.windowPosition.y,
        width: this.windowSize.width,
        height: this.windowSize.height
      })
    }

    if (!store.get("openaiApiKey")) {
      this.mainWindow.setIgnoreMouseEvents(false)
    } else {
      this.mainWindow.setIgnoreMouseEvents(true)
    }
    this.mainWindow.setVisibleOnAllWorkspaces(true, {
      visibleOnFullScreen: true
    })
    this.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)
    this.mainWindow.setContentProtection(true)
    // 恢复用户设置的透明度，而不是强制设置为1
    this.mainWindow.setOpacity(this.userOpacity)

    this.mainWindow.showInactive()

    if (focusedWindow && !focusedWindow.isDestroyed()) {
      focusedWindow.focus()
    }

    this.isWindowVisible = true
    

  }

  public toggleMainWindow(): void {
    if (this.isWindowVisible) {
      this.hideMainWindow()
      this.appState.mouseTrackingHelper.stopIoHook()
    } else {
      this.showMainWindow()
      this.appState.mouseTrackingHelper.startIoHook()
    }
  }

  // New methods for window movement
  public moveWindowRight(): void {
    if (!this.mainWindow) return

    const windowWidth = this.windowSize?.width || 0

    // Ensure currentX and currentY are numbers
    this.currentX = Number(this.currentX) || 0
    this.currentY = Number(this.currentY) || 0

    // Allow window to move beyond screen but keep at least 20% visible on the left
    const minVisibleWidth = windowWidth * 0.2
    const maxX = this.screenWidth - minVisibleWidth

    this.currentX = Math.min(maxX, this.currentX + this.step)
    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    )
    
    // Emit event for window moved
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send("window-moved");
    }
  }

  public moveWindowLeft(): void {
    if (!this.mainWindow) return

    const windowWidth = this.windowSize?.width || 0

    // Ensure currentX and currentY are numbers
    this.currentX = Number(this.currentX) || 0
    this.currentY = Number(this.currentY) || 0

    // Allow window to move beyond left edge but keep at least 20% visible on the right
    const minVisibleWidth = windowWidth * 0.2
    const minX = -(windowWidth - minVisibleWidth)

    this.currentX = Math.max(minX, this.currentX - this.step)
    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    )
    
    // Emit event for window moved
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send("window-moved");
    }
  }

  public moveWindowDown(): void {
    if (!this.mainWindow) return;

    this.currentY = Number(this.currentY) || 0;
    
    // 无限制向下移动
    this.currentY = this.currentY + this.step;

    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    );
    
    // Emit event for window moved
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send("window-moved");
    }
  }

  public moveWindowUp(): void {
    if (!this.mainWindow) return;
    
    this.currentY = Number(this.currentY) || 0;
    
    // 无限制向上移动
    this.currentY = this.currentY - this.step;

    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    );
    
    // Emit event for window moved
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send("window-moved");
    }
  }

  public resetWindowPosition(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;

    // 获取主屏幕信息
    const primaryDisplay = screen.getPrimaryDisplay();
    const workArea = primaryDisplay.workAreaSize;

    // 获取当前窗口位置和尺寸
    const [currentX, currentY] = this.mainWindow.getPosition();
    const windowWidth = this.windowSize?.width || 0;
    const windowHeight = this.windowSize?.height || 0;


    console.log(`Current window position: (${currentX}, ${currentY}), size: (${windowWidth}x${windowHeight})`);

    // 检查窗口是否完全不在屏幕区域内
    const isCompletelyOffScreen =
      (currentX + windowWidth <= 0) ||  // 完全在左边外
      (currentX >= workArea.width) ||   // 完全在右边外
      (currentY + windowHeight <= 0) || // 完全在上边外
      (currentY >= workArea.height);    // 完全在下边外

    if (isCompletelyOffScreen) {
      // 只有当窗口几乎完全不可见时，才重置到左上角位置
      this.currentX = 20; // 留一点边距
      this.currentY = 20; // 留一点边距

      this.mainWindow.setPosition(
        Math.round(this.currentX),
        Math.round(this.currentY)
      );

      // Update internal state
      this.windowPosition = { x: this.currentX, y: this.currentY };

      console.log("Window was off-screen and has been reset to top-left position");
    } else {
      // 窗口在屏幕内或部分可见，位置不变
      console.log("Window is visible, position remains unchanged");
    }
  }

  /**
   * 减小窗口透明度
   */
  public decreaseOpacity(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;

    const currentOpacity = this.mainWindow.getOpacity();
    const newOpacity = Math.max(0.1, currentOpacity - 0.1); // 最小透明度为0.1，每次减少0.1

    this.mainWindow.setOpacity(newOpacity);
    // 保存用户设置的透明度
    this.userOpacity = newOpacity;
    console.log(`Window opacity decreased to: ${newOpacity}`);
  }

  /**
   * 增大窗口透明度
   */
  public increaseOpacity(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;

    const currentOpacity = this.mainWindow.getOpacity();
    const newOpacity = Math.min(1.0, currentOpacity + 0.1); // 最大透明度为1.0，每次增加0.1

    this.mainWindow.setOpacity(newOpacity);
    // 保存用户设置的透明度
    this.userOpacity = newOpacity;
    console.log(`Window opacity increased to: ${newOpacity}`);
  }
}
