import { app, shell, systemPreferences, desktopCapturer } from 'electron';


// 权限状态接口 - 只保留必需的权限
interface PermissionStatus {
  accessibility: boolean;
  screen: boolean;
}

/**
 * 请求应用所需的系统权限
 * 包括辅助功能和屏幕录制权限（仅 macOS）
 */
export async function requestPermissions(): Promise<void> {
  if (process.platform === 'darwin') {
    await requestMacOSPermissions();
  }
  // 非 macOS 平台不需要权限请求
}

/**
 * 请求辅助功能权限
 */
export async function requestAccessibilityPermissions(): Promise<void> {
  if (process.platform === 'darwin') {
     // 检查辅助功能权限
    if (!systemPreferences.isTrustedAccessibilityClient(false)) {
      console.log('请求辅助功能权限...');
      systemPreferences.isTrustedAccessibilityClient(true);
      shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility');
    }
  }
}

/**
 * 请求屏幕录制权限
 */
export async function requestScreenRecordingPermissions(): Promise<void> {
  if (process.platform === 'darwin') {
    // 检查屏幕录制权限
    const screenStatus = systemPreferences.getMediaAccessStatus('screen');
    console.log('屏幕录制权限状态:', screenStatus);

    if (screenStatus !== 'granted') {
      try {
        // 引导用户手动授权屏幕录制权限
        console.log('引导用户手动授权屏幕录制权限');
        shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture');
      } catch (error) {
        console.error('处理屏幕录制权限时出错:', error);
      }
    }
  }
}

/**
 * 请求 macOS 平台所需的权限
 */
async function requestMacOSPermissions(): Promise<void> {
  try {
    console.log('开始请求 macOS 权限...');

    // 检查辅助功能权限
    if (!systemPreferences.isTrustedAccessibilityClient(false)) {
      console.log('请求辅助功能权限...');
      systemPreferences.isTrustedAccessibilityClient(true);
      shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility');
    }

    // 检查屏幕录制权限（用于系统音频捕获和截图）
    let screenCaptureStatus = systemPreferences.getMediaAccessStatus('screen');
    console.log('当前屏幕录制权限状态:', screenCaptureStatus);

    if (screenCaptureStatus !== 'granted') {
      try {
        console.log('请求屏幕录制权限...');
        
        // 尝试触发权限请求（通过尝试获取屏幕源）
        try {
          const sources = await desktopCapturer.getSources({ types: ['screen'] });
          console.log('屏幕源获取成功，权限可能已授予');
        } catch (captureError) {
          console.log('屏幕源获取失败，需要手动授权:', captureError);
        }
        
        // 引导用户手动授权屏幕录制权限
        console.log('引导用户手动授权屏幕录制权限');
        shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture');
      } catch (error) {
        console.error('处理屏幕录制权限时出错:', error);
      }
    }

    console.log('macOS 权限检查完成');
  } catch (error) {
    console.error('请求 macOS 权限时出错:', error);
  }
}

/**
 * 检查是否已授予所有必要权限（仅 macOS）
 * @returns 包含各权限状态的对象
 */
export async function checkPermissions(): Promise<PermissionStatus> {
  const result: PermissionStatus = {
    accessibility: false,
    screen: false
  };

  if (process.platform === 'darwin') {
    // 检查辅助功能权限
    result.accessibility = systemPreferences.isTrustedAccessibilityClient(false);

    // 检查屏幕录制权限
    const screenStatus = systemPreferences.getMediaAccessStatus('screen');
    console.log('屏幕录制权限状态:', screenStatus);
    
    if (screenStatus === 'granted') {
      result.screen = true;
    }
    
    // 如果状态不是 granted，尝试通过二进制文件进一步检查
    if (screenStatus === 'denied' || screenStatus === 'restricted') {
      result.screen = false;
    }

  } else {
    // 非 macOS 平台默认假设权限已授予
    result.accessibility = true;
    result.screen = true;
  }

  return result;
}