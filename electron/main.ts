import { app, BrowserWindow, ipc<PERSON>ain, IpcMainInvokeEvent } from "electron";
import { registerMicrophoneHandlers } from './ipc/microphoneHandlers';
import { registerSystemAudioHandlers } from './ipc/systemAudioHandlers';
import { setupVoiceHandlers } from './ipc/voiceHandlers';
import { initializeIpcHandlers } from "./ipcHandlers";
import { MouseTrackingHelper } from "./MouseTrackingHelper";
import { ProcessingHelper } from "./ProcessingHelper";
import { ScreenshotHelper } from "./ScreenshotHelper";
import { ShortcutsHelper } from "./shortcuts";
import { WindowHelper } from "./WindowHelper";

// 条件导入安全模块（仅在生产环境）
let antiDebugModule: any = null;
let runtimeProtectionModule: any = null;
if (process.env.NODE_ENV === 'production') {
  try {
    antiDebugModule = require("./security/anti-debug");
    runtimeProtectionModule = require("./security/runtime-protection");
  } catch (error) {
    console.warn("⚠️ 无法加载安全模块:", error);
  }
}

// 延迟初始化这些管理器，避免循环依赖
let systemASRManager: any = null;
let microphoneASRManager: any = null;
let systemAudioManager: any = null;
let microphoneManager: any = null;

// 添加标志，确保IPC处理器只注册一次
let systemAudioHandlersRegistered = false;
let microphoneHandlersRegistered = false;
let voiceHandlersRegistered = false;

export class AppState {
  private static instance: AppState | null = null

  public windowHelper: WindowHelper
  private screenshotHelper: ScreenshotHelper
  public shortcutsHelper: ShortcutsHelper
  public processingHelper: ProcessingHelper
  public mouseTrackingHelper: MouseTrackingHelper

  // View management
  private view: "queue" | 'voice' |"solutions" = "queue"

  private problemInfo: {
    problem_statement: string
    input_format: Record<string, any>
    output_format: Record<string, any>
    constraints: Array<Record<string, any>>
    test_cases: Array<Record<string, any>>
  } | null = null // Allow null

  private hasDebugged: boolean = false

  // Processing events
  public readonly PROCESSING_EVENTS = {
    //global states
    UNAUTHORIZED: "procesing-unauthorized",
    NO_SCREENSHOTS: "processing-no-screenshots",
    CONFIG_INIT_FAILED: "config-init-failed",
    API_KEY_OUT_OF_CREDITS: "processing-api-key-out-of-credits",
    API_KEY_INVALID: "processing-api-key-invalid",

    //states for generating the initial solution
    INITIAL_START: "initial-start",
    PROBLEM_EXTRACTED: "problem-extracted",
    SOLUTION_SUCCESS: "solution-success",
    INITIAL_SOLUTION_ERROR: "solution-error",

    //states for processing the debugging
    DEBUG_START: "debug-start",
    DEBUG_SUCCESS: "debug-success",
    DEBUG_ERROR: "debug-error"
  } as const

  constructor() {
    // Initialize WindowHelper with this
    this.windowHelper = new WindowHelper(this)

    // Initialize ScreenshotHelper
    this.screenshotHelper = new ScreenshotHelper(this.view)

    // Initialize ProcessingHelper
    this.processingHelper = new ProcessingHelper(this)

    // Initialize ShortcutsHelper
    this.shortcutsHelper = new ShortcutsHelper(this)
    
    // Initialize MouseTrackingHelper
    this.mouseTrackingHelper = new MouseTrackingHelper(this)
  }

  public static getInstance(): AppState {
    if (!AppState.instance) {
      AppState.instance = new AppState()
    }
    return AppState.instance
  }

  // Getters and Setters
  public getMainWindow(): BrowserWindow | null {
    return this.windowHelper.getMainWindow()
  }

  public getView(): "queue" | "solutions" | 'voice' {
    return this.view
  }

  public setView(view: "queue" | "solutions" | 'voice'): void {
    const previousView = this.view;
    this.view = view
    this.screenshotHelper.setView(view)
  }

  public isVisible(): boolean {
    return this.windowHelper.isVisible()
  }

  public getScreenshotHelper(): ScreenshotHelper {
    return this.screenshotHelper
  }

  public getProblemInfo(): any {
    return this.problemInfo
  }

  public setProblemInfo(problemInfo: any): void {
    this.problemInfo = problemInfo
  }

  public getScreenshotQueue(): string[] {
    return this.screenshotHelper.getScreenshotQueue()
  }

  public getExtraScreenshotQueue(): string[] {
    return this.screenshotHelper.getExtraScreenshotQueue()
  }

  // Window management methods
  public createWindow(): void {
    this.windowHelper.createWindow()
    
    // Set main window for managers
    const mainWindow = this.getMainWindow();
    if (mainWindow) {
      // 设置最大监听器数量，避免内存泄漏警告
      mainWindow.webContents.setMaxListeners(30); // 增加最大监听器数量
      
      // 初始化系统音频管理器
      if (!systemAudioManager) {
        console.log('初始化SystemAudioManager...');
        import('./handlers/SystemAudioManager').then(({ systemAudioManager: manager }) => {
          systemAudioManager = manager;
          systemAudioManager.setMainWindow(mainWindow);
          
          // 注册系统音频IPC处理器
          if (!systemAudioHandlersRegistered) {
            console.log('注册系统音频IPC处理器...');
            registerSystemAudioHandlers(mainWindow);
            systemAudioHandlersRegistered = true;
          }
        }).catch(error => {
          console.error('Failed to import SystemAudioManager:', error);
        });
      } else {
        systemAudioManager.setMainWindow(mainWindow);
      }
      
      // 初始化麦克风管理器
      if (!microphoneManager) {
        console.log('初始化MicrophoneManager...');
        import('./handlers/MicrophoneManager').then(({ microphoneManager: manager }) => {
          microphoneManager = manager;
          microphoneManager.setMainWindow(mainWindow);
          
          // 注册麦克风IPC处理器
          if (!microphoneHandlersRegistered) {
            console.log('注册麦克风IPC处理器...');
            registerMicrophoneHandlers(mainWindow);
            microphoneHandlersRegistered = true;
          }
        }).catch(error => {
          console.error('Failed to import MicrophoneManager:', error);
        });
      } else {
        microphoneManager.setMainWindow(mainWindow);
      }
    

      // 注册语音处理器
      if (!voiceHandlersRegistered) {
        console.log('注册语音处理IPC处理器...');
        setupVoiceHandlers(mainWindow);
        voiceHandlersRegistered = true;
      }
    }
  }

  public hideMainWindow(): void {
    this.windowHelper.hideMainWindow()
  }

  public showMainWindow(): void {
    this.windowHelper.showMainWindow()
  }

  public toggleMainWindow(): void {
    this.windowHelper.toggleMainWindow()
  }

  public setWindowDimensions(width: number, height: number): void {
    this.windowHelper.setWindowDimensions(width, height)
  }

  public clearQueues(): void {
    this.screenshotHelper.clearQueues()

    // Clear problem info
    this.problemInfo = null

    // Reset view to initial state
    this.setView("queue")
  }

  // Screenshot management methods
  public async takeScreenshot(): Promise<string> {
    if (!this.getMainWindow()) throw new Error("No main window available")

    const mainWindow = this.getMainWindow();
    if (!mainWindow) {
      throw new Error("No main window available");
    }

    const screenshotPath = await this.screenshotHelper.takeScreenshot(
      () => this.hideMainWindow(),
      () => this.showMainWindow(),
      mainWindow
    );

    return screenshotPath
  }


  // 添加到 AppState 类中
  public async takeRegionScreenshot(
    x: number,
    y: number,
    width: number,
    height: number
  ): Promise<string> {
    if (!this.getMainWindow()) throw new Error("No main window available");

    const screenshotPath = await this.screenshotHelper.captureRegionScreenshot(
      x,
      y,
      width,
      height,
      () => this.hideMainWindow(),
      () => this.showMainWindow()
    );

    return screenshotPath;
  }
  public async getImagePreview(filepath: string): Promise<string> {
    return this.screenshotHelper.getImagePreview(filepath)
  }

  public async deleteScreenshot(
    path: string
  ): Promise<{ success: boolean; error?: string }> {
    return this.screenshotHelper.deleteScreenshot(path)
  }

  // New methods to move the window
  public moveWindowLeft(): void {
    this.windowHelper.moveWindowLeft()
  }

  public moveWindowRight(): void {
    this.windowHelper.moveWindowRight()
  }

  public moveWindowDown(): void {
    this.windowHelper.moveWindowDown()
  }

  public moveWindowUp(): void {
    this.windowHelper.moveWindowUp()
  }

  public decreaseOpacity(): void {
    this.windowHelper.decreaseOpacity()
  }

  public increaseOpacity(): void {
    this.windowHelper.increaseOpacity()
  }

  public setHasDebugged(value: boolean): void {
    this.hasDebugged = value
  }

  public getHasDebugged(): boolean {
    return this.hasDebugged
  }

  public getSystemAudioManager(): any {
    return systemAudioManager;
  }

  public getMicrophoneManager(): any {
    return microphoneManager;
  }

  public setIgnoreMouseEvents(value: boolean): void {
    this.windowHelper.setIgnoreMouseEvents(value)
  }

  /**
   * 清理所有资源，确保应用完全退出
   */
  public dispose(): void {
    console.log("AppState: 开始清理所有资源...");

    try {
      // 停止所有语音识别相关功能
      this.stopAllVoiceRecognitionServices();

      // 停止鼠标监听程序 (ioHook)
      if (this.mouseTrackingHelper) {
        console.log("AppState: 停止鼠标监听程序...");
        this.mouseTrackingHelper.stopIoHook();
      }

      // 清理全局快捷键
      if (this.shortcutsHelper) {
        console.log("AppState: 清理全局快捷键...");
        this.shortcutsHelper.dispose();
      }

      // 取消正在进行的处理请求
      if (this.processingHelper) {
        console.log("AppState: 取消处理请求...");
        this.processingHelper.cancelOngoingRequests();
      }

      // 清理截图队列
      if (this.screenshotHelper) {
        console.log("AppState: 清理截图队列...");
        this.screenshotHelper.clearQueues();
      }

      // 停止运行时保护（如果在生产环境中启用）
      if (runtimeProtectionModule && typeof runtimeProtectionModule.stopRuntimeProtection === 'function') {
        console.log("AppState: 停止运行时保护...");
        runtimeProtectionModule.stopRuntimeProtection();
      }

      console.log("AppState: 资源清理完成");
    } catch (error) {
      console.error("AppState: 清理资源时出错:", error);
    }
  }

  /**
   * 停止所有语音识别相关服务
   */
  private stopAllVoiceRecognitionServices(): void {
    console.log("AppState: 开始停止所有语音识别服务...");

    try {
      const mainWindow = this.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        // 使用现有的停止语音识别流程函数
        stopVoiceRecognitionFlow(mainWindow, true, true).then((success) => {
          if (success) {
            console.log("AppState: 语音识别服务停止成功");
          } else {
            console.warn("AppState: 语音识别服务停止失败");
          }
        }).catch((error) => {
          console.error("AppState: 停止语音识别服务时出错:", error);
        });
      } else {
        console.warn("AppState: 主窗口不可用，直接清理语音识别资源");
        // 如果主窗口不可用，直接清理资源
        this.forceCleanupVoiceResources();
      }
    } catch (error) {
      console.error("AppState: 停止语音识别服务时出错:", error);
      // 出错时强制清理资源
      this.forceCleanupVoiceResources();
    }
  }

  /**
   * 强制清理语音识别资源（当主窗口不可用时）
   */
  private forceCleanupVoiceResources(): void {
    console.log("AppState: 强制清理语音识别资源...");

    try {
      // 直接调用各个管理器的停止方法
      if (systemASRManager) {
        console.log("AppState: 强制停止 SystemASRManager...");
        systemASRManager.stopASRSession();
      }

      if (microphoneASRManager) {
        console.log("AppState: 强制停止 MicrophoneASRManager...");
        microphoneASRManager.stopASRSession();
      }

      if (systemAudioManager) {
        console.log("AppState: 强制停止 SystemAudioManager...");
        systemAudioManager.stopCapturing();
      }

      if (microphoneManager) {
        console.log("AppState: 强制停止 MicrophoneManager...");
        microphoneManager.stopCapturing();
      }

      console.log("AppState: 语音识别资源强制清理完成");
    } catch (error) {
      console.error("AppState: 强制清理语音识别资源时出错:", error);
    }
  }
}

/**
 * 启动语音识别流程 - 优化版本，支持独立启动
 * @param mainWindow 主窗口实例
 * @param enableSystemAudio 是否启动系统音频识别
 * @param enableMicrophone 是否启动麦克风识别
 * @returns 启动是否成功
 */
export async function startVoiceRecognitionFlow(
  mainWindow: BrowserWindow, 
  enableSystemAudio: boolean = true, 
  enableMicrophone: boolean = false
): Promise<boolean> {
  console.log(`开始启动语音识别流程... 系统音频: ${enableSystemAudio}, 麦克风: ${enableMicrophone}`);
  
  let systemAudioSuccess = true;
  let microphoneSuccess = true;
  
  try {
    // 根据选项独立启动系统音频和麦克风
    const startPromises: Promise<boolean>[] = [];
    
    if (enableSystemAudio) {
      startPromises.push(startSystemAudioFlow(mainWindow));
    }
    
    if (enableMicrophone) {
      startPromises.push(startMicrophoneFlow(mainWindow));
    }
    
    if (startPromises.length === 0) {
      console.warn('语音识别流程：没有选择任何音频源');
      return false;
    }
    
    // 并行启动所有选中的音频源
    const results = await Promise.allSettled(startPromises);
    
    let successCount = 0;
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        successCount++;
      } else {
        const source = index === 0 && enableSystemAudio ? '系统音频' : '麦克风';
        console.error(`${source}启动失败:`, result.status === 'rejected' ? result.reason : '未知错误');
      }
    });
    
    const overallSuccess = successCount > 0;
    
    // 通知渲染进程语音识别状态
    mainWindow.webContents.send('toggle-voice-assistant', { 
      asrConnected: overallSuccess, 
      recording: overallSuccess,
      status: overallSuccess ? 'active' : 'error',
      systemAudioActive: enableSystemAudio && systemAudioSuccess,
      microphoneActive: enableMicrophone && microphoneSuccess,
      error: overallSuccess ? undefined : '语音识别服务启动失败'
    });
    
    return overallSuccess;
  } catch (error) {
    console.error('启动语音识别流程时出错:', error);
    
    mainWindow.webContents.send('toggle-voice-assistant', { 
      asrConnected: false, 
      recording: false,
      status: 'error',
      error: error instanceof Error ? error.message : '启动语音识别流程失败'
    });
    
    return false;
  }
}

/**
 * 启动系统音频识别流程
 */
async function startSystemAudioFlow(mainWindow: BrowserWindow): Promise<boolean> {
  console.log('启动系统音频识别流程...');
  
  try {
    // 导入SystemAudioManager
    const { systemAudioManager } = await import('./handlers/SystemAudioManager');
    const { systemASRManager } = await import('./handlers/SystemASRManager');
    
    // 确保设置主窗口
    systemAudioManager.setMainWindow(mainWindow);
    systemASRManager.setMainWindow(mainWindow);
    
    // 启动系统音频捕获（内部会先启动SystemASR服务，再启动音频捕获）
    const result = await systemAudioManager.startCapturing();
    
    if (result.success) {
      console.log('系统音频识别流程启动成功');
      return true;
    } else {
      console.error('系统音频识别流程启动失败:', result.error);
      return false;
    }
  } catch (error) {
    console.error('启动系统音频识别流程时出错:', error);
    return false;
  }
}

/**
 * 启动麦克风识别流程
 */
async function startMicrophoneFlow(mainWindow: BrowserWindow): Promise<boolean> {
  console.log('启动麦克风识别流程...');
  
  try {
    // 导入MicrophoneManager
    const { microphoneManager } = await import('./handlers/MicrophoneManager');
    const { microphoneASRManager } = await import('./handlers/MicrophoneASRManager'); 
    
    // 确保设置主窗口
    microphoneManager.setMainWindow(mainWindow);
    microphoneASRManager.setMainWindow(mainWindow);
    
    // 启动麦克风音频捕获（内部会先启动MicrophoneASR服务，再启动音频捕获）
    const result = await microphoneManager.startCapturing();
    
    if (result.success) {
      console.log('麦克风识别流程启动成功');
      return true;
    } else {
      console.error('麦克风识别流程启动失败:', result.error);
      return false;
    }
  } catch (error) {
    console.error('启动麦克风识别流程时出错:', error);
    return false;
  }
}

/**
 * 停止语音识别流程 - 优化版本，支持独立停止
 */
export async function stopVoiceRecognitionFlow(
  mainWindow: BrowserWindow,
  stopSystemAudio: boolean = true,
  stopMicrophone: boolean = true
): Promise<boolean> {
  console.log(`停止语音识别流程... 系统音频: ${stopSystemAudio}, 麦克风: ${stopMicrophone}`);
  
  try {
    const stopPromises: Promise<void>[] = [];
    
    if (stopSystemAudio) {
      stopPromises.push(stopSystemAudioFlow());
    }
    
    if (stopMicrophone) {
      stopPromises.push(stopMicrophoneFlow());
    }
    
    // 并行停止所有选中的音频源
    await Promise.allSettled(stopPromises);
    
    // 通知渲染进程语音识别已停止
    mainWindow.webContents.send('toggle-voice-assistant', { 
      asrConnected: false, 
      recording: false,
      status: 'stopped'
    });
    
    return true;
  } catch (error) {
    console.error('停止语音识别流程时出错:', error);
    
    mainWindow.webContents.send('toggle-voice-assistant', { 
      asrConnected: false, 
      recording: false,
      status: 'error',
      error: error instanceof Error ? error.message : '停止语音识别流程失败'
    });
    
    return false;
  }
}

/**
 * 停止系统音频识别流程
 */
async function stopSystemAudioFlow(): Promise<void> {
  try {
    // 导入SystemASRManager确保正确停止
    const { systemASRManager } = await import('./handlers/SystemASRManager');
    
    // 停止系统音频捕获
    if (systemAudioManager) {
      await systemAudioManager.stopCapturing();
      console.log('系统音频捕获已停止');
    }
    
    // 停止SystemASR服务
    if (systemASRManager) {
      systemASRManager.stopASRSession();
      console.log('SystemASR服务已停止');
    }
  } catch (error) {
    console.error('停止系统音频识别流程时出错:', error);
  }
}

/**
 * 停止麦克风识别流程
 */
async function stopMicrophoneFlow(): Promise<void> {
  try {
    // 导入MicrophoneASRManager确保正确停止
    const { microphoneASRManager } = await import('./handlers/MicrophoneASRManager');

    // 停止麦克风音频捕获
    if (microphoneManager) {
      await microphoneManager.stopCapturing();
      console.log('麦克风音频捕获已停止');
    }

    // 明确停止MicrophoneASR服务
    if (microphoneASRManager) {
      microphoneASRManager.stopASRSession();
      console.log('MicrophoneASR服务已停止');
    }
  } catch (error) {
    console.error('停止麦克风识别流程时出错:', error);
  }
}


// Initialize the app
async function initializeApp() {
  console.log("initializeApp: Starting app initialization...");

  try {
    // Initialize app state
    console.log("initializeApp: Creating app state...");
    const appState = AppState.getInstance()

    // Create main window
    console.log("initializeApp: Creating main window...");
    appState.createWindow()

    // Initialize IPC handlers
    console.log("initializeApp: Initializing IPC handlers...");
    initializeIpcHandlers(appState)

    // Initialize auto updater
    // console.log("initializeApp: Initializing auto updater...");
    // initAutoUpdater()

    // 确保快捷键被注册
    console.log("initializeApp: 注册全局快捷键...");
    appState.shortcutsHelper.registerGlobalShortcuts();

    // 检查并初始化配置 - 等待窗口内容加载完成后进行
    console.log("initializeApp: 等待窗口加载完成后检查配置...");
    const mainWindow = appState.getMainWindow();
    if (mainWindow) {
      // 等待窗口内容加载完成
      if (mainWindow.webContents.isLoading()) {
        console.log("initializeApp: 窗口正在加载，等待加载完成...");
        await new Promise<void>((resolve) => {
          mainWindow.webContents.once('did-finish-load', () => {
            console.log("initializeApp: 窗口加载完成，开始配置检查");
            resolve();
          });
        });
      }

      // 额外等待一小段时间确保渲染进程完全准备好
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log("initializeApp: 开始检查并初始化配置...");
      await checkAndInitializeConfig(mainWindow);
    }

    // 获取权限 - 启动时自动请求权限
    try {
      console.log("initializeApp: 开始请求系统权限...");
      const { checkPermissions, requestAccessibilityPermissions, requestScreenRecordingPermissions } = await import("./privilege");
      // 先检查权限
      const result = await checkPermissions();
      if (!result.accessibility) {
        requestAccessibilityPermissions()
      }
      if (!result.screen) {
        requestScreenRecordingPermissions()
      }
    } catch (error) {
      console.error("initializeApp: 权限请求失败:", error);
      // 权限请求失败不应该阻止应用启动，继续执行
    }
    
    console.log("initializeApp: 应用初始化完成");
  } catch (error) {
    console.error("initializeApp: 应用初始化失败:", error);
    throw error;
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit()
  }
})

app.on("activate", () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    AppState.getInstance().createWindow()
  }
})

// 单实例控制 - 确保只有一个应用实例运行
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  console.log("应用已在运行，退出当前实例");
  app.quit();
} else {
  // 当尝试启动第二个实例时，聚焦到第一个实例的窗口
  app.on('second-instance', () => {
    console.log("检测到第二个实例启动，聚焦到现有窗口");

    const appState = AppState.getInstance();
    const mainWindow = appState.getMainWindow();

    if (mainWindow) {
      // 如果窗口被最小化，恢复它
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }

      // 显示并聚焦窗口
      mainWindow.show();
      mainWindow.focus();

      console.log("已聚焦到现有窗口");
    } else {
      console.warn("主窗口不存在，无法聚焦");
    }
  });

  // This method will be called when Electron has finished
  // initialization and is ready to create browser windows.
  // Some APIs can only be used after this event occurs.
  console.log("Setting up app.whenReady() handler...");
  app.whenReady().then(() => {
    console.log("Electron app is ready, starting initialization...");

    // 初始化安全保护
    if (process.env.NODE_ENV === 'production') {
      console.log("🛡️ 初始化安全保护...");
      try {
        if (antiDebugModule) {
          antiDebugModule.initAntiDebug();
          console.log("✅ 反调试保护已启动");
        }
        if (runtimeProtectionModule) {
          runtimeProtectionModule.initRuntimeProtection();
          console.log("✅ 运行时保护已启动");
        }
      } catch (error) {
        console.warn("⚠️ 安全保护初始化失败:", error);
      }
    }

    return initializeApp();
  }).catch((error) => {
    console.error("Failed to initialize app:", error);
  });
}

// 添加额外的调试信息
console.log("Main.ts loaded, app state:", {
  isReady: app.isReady(),
  version: process.versions.electron
});

// 添加应用事件监听器来调试启动过程
app.on('ready', () => {
  console.log("App 'ready' event fired");
});

app.on('before-quit', (event) => {
  console.log("App 'before-quit' event fired");

  // 开始清理所有资源
  try {
    const appState = AppState.getInstance();

    // 停止鼠标监听程序 (ioHook)
    console.log("正在停止鼠标监听程序...");
    if (appState.mouseTrackingHelper) {
      appState.mouseTrackingHelper.stopIoHook();
    }

    // 清理全局快捷键
    console.log("正在清理全局快捷键...");
    if (appState.shortcutsHelper) {
      appState.shortcutsHelper.dispose();
    }

    // 取消正在进行的处理请求
    console.log("正在取消处理请求...");
    if (appState.processingHelper) {
      appState.processingHelper.cancelOngoingRequests();
    }

  } catch (error) {
    console.error("清理资源时出错:", error);
  }
});

app.on('will-quit', (event) => {
  console.log("App 'will-quit' event fired");
});

// Clean up on app quit
app.on('quit', () => {
  console.log("App 'quit' event fired - 开始最终清理");

  try {
    // Clean up SystemASRManager
    if (systemASRManager) {
      console.log("清理 SystemASRManager...");
        systemASRManager.dispose();
    }

    // Clean up MicrophoneASRManager
    if (microphoneASRManager) {
      console.log("清理 MicrophoneASRManager...");
        microphoneASRManager.dispose();
    }

    // Clean up SystemAudioManager
    if (systemAudioManager) {
      console.log("清理 SystemAudioManager...");
        systemAudioManager.dispose();
    }

    // Clean up MicrophoneManager
    if (microphoneManager) {
      console.log("清理 MicrophoneManager...");
        microphoneManager.dispose();
    }

    console.log("所有资源清理完成");
  } catch (error) {
    console.error("最终清理时出错:", error);
  }
});

// 启动系统音频ASR
ipcMain.handle('start-system-asr', async (event: IpcMainInvokeEvent) => {
  try {
    if (!systemASRManager) {
      const { systemASRManager: manager } = await import('./handlers/SystemASRManager');
      systemASRManager = manager;
    }
    return await systemASRManager.startASRSession();
  } catch (error) {
    console.error('启动系统音频ASR服务失败:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '启动系统音频ASR服务失败' 
    };
  }
});

// 启动麦克风ASR
ipcMain.handle('start-microphone-asr', async (event: IpcMainInvokeEvent) => {
  try {
    if (!microphoneASRManager) {
      const { microphoneASRManager: manager } = await import('./handlers/MicrophoneASRManager');
      microphoneASRManager = manager;
    }
    return await microphoneASRManager.startASRSession();
  } catch (error) {
    console.error('启动麦克风ASR服务失败:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '启动麦克风ASR服务失败' 
    };
  }
});

// 停止系统音频ASR
ipcMain.handle('stop-system-asr', async (event: IpcMainInvokeEvent) => {
  try {
    if (systemASRManager) {
      systemASRManager.stopASRSession();
    }
    return { success: true };
  } catch (error) {
    console.error('停止系统音频ASR服务失败:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '停止系统音频ASR服务失败' 
    };
  }
});

// 停止麦克风ASR
ipcMain.handle('stop-microphone-asr', async (event: IpcMainInvokeEvent) => {
  try {
    if (microphoneASRManager) {
      microphoneASRManager.stopASRSession();
    }
    return { success: true };
  } catch (error) {
    console.error('停止麦克风ASR服务失败:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '停止麦克风ASR服务失败' 
    };
  }
});

/**
 * 检查并初始化配置
 */
async function checkAndInitializeConfig(mainWindow: BrowserWindow | null) {
  console.log("checkAndInitializeConfig called");
  if (!mainWindow) return;

  const { store } = await import("./store");
  const { initConfig } = await import("./handlers/problemHandler");

  try {
    const apiKey = store.get("openaiApiKey");

    if (apiKey) {
      // store 中存在 key，直接调用 initConfig 方法初始化配置
      console.log('检测到已存储的 API Key，开始初始化配置...');

      const configResult = await initConfig(apiKey);

      console.log('configResult:', configResult);

      if (!configResult.success) {

        // 发送配置初始化失败事件到渲染进程
        if (mainWindow && !mainWindow.isDestroyed()) {

          const errorData = {
            message: configResult.message || '配置初始化失败',
            detail: 'Please contact Java_Plus to check your API Key'
          };
          try {
            mainWindow.webContents.send("config-init-failed", errorData);
          } catch (sendError) {
            console.error('❌ 发送配置错误事件失败:', sendError);
          }
        } else {
          console.log('mainWindow:', !!mainWindow);
          if (mainWindow) {
            console.log('mainWindow.isDestroyed():', mainWindow.isDestroyed());
          }
        }
        // 5秒后退出程序
        console.log('⏰ 将在5秒后退出应用...');
        setTimeout(() => {
          console.log('🚪 退出应用');
          app.quit();
        }, 5000);

        return;
      }

      console.log('配置初始化成功');

      // 配置初始化成功后，更新工具条上的模型列表和编程语言
      console.log('更新工具条上的模型列表和编程语言...');
      try {
        const { store } = await import("./store");
        const { models, getCodeLanguages, getDefaultCodeLanguage } = await import("./store");
        
        // 获取当前模型信息
        const curModel = store.get("curModel") || models[0];
        const modelIndex = models.findIndex(model => model === curModel) + 1;
        
        // 获取当前编程语言信息
        const codeLanguages = getCodeLanguages();
        const curCodeLanguage = store.get("curCodeLanguage") || getDefaultCodeLanguage();
        const codeLanguageIndex = codeLanguages.findIndex(lang => lang === curCodeLanguage) + 1;
        
        // 发送模型变更事件
        mainWindow.webContents.send("model-changed", {
          model: curModel,
          index: modelIndex,
          total: models.length
        });
        console.log('✅ 模型信息已更新:', { model: curModel, index: modelIndex, total: models.length });
        
        // 发送编程语言变更事件
        mainWindow.webContents.send("code-language-changed", {
          codeLanguage: curCodeLanguage,
          index: codeLanguageIndex,
          total: codeLanguages.length
        });
        console.log('✅ 编程语言信息已更新:', { codeLanguage: curCodeLanguage, index: codeLanguageIndex, total: codeLanguages.length });
        
      } catch (updateError) {
        console.error('❌ 更新模型和编程语言信息失败:', updateError);
      }

      // 配置初始化成功后，通知渲染进程更新麦克风按钮状态
      console.log('通知渲染进程更新麦克风按钮状态...');
      try {
        mainWindow.webContents.send("config-updated", {
          success: true,
          message: '配置初始化成功'
        });
        console.log('✅ 配置更新事件已发送到渲染进程');
      } catch (sendError) {
        console.error('❌ 发送配置更新事件失败:', sendError);
      }
    } else {
      // store 中不存在 key，会显示输入 key 界面（由前端处理）
      console.log('未检测到 API Key，等待用户输入...');
    }
  } catch (error) {
    console.error('检查配置时出错:', error);
  }
}

// 添加 IPC 处理器用于配置初始化
ipcMain.handle('init-config', async (event: IpcMainInvokeEvent, apiKey: string) => {
  try {
    const { initConfig } = await import("./handlers/problemHandler");
    const result = await initConfig(apiKey);

    // 如果配置初始化成功，通知所有窗口更新配置状态
    if (result.success) {
      console.log('IPC 配置初始化成功，通知渲染进程更新状态...');
      
      // 预先导入所需模块
      const { store } = await import("./store");
      const { models, getCodeLanguages, getDefaultCodeLanguage } = await import("./store");
      
      // 获取当前模型信息
      const curModel = store.get("curModel") || models[0];
      const modelIndex = models.findIndex(model => model === curModel) + 1;
      
      // 获取当前编程语言信息
      const codeLanguages = getCodeLanguages();
      const curCodeLanguage = store.get("curCodeLanguage") || getDefaultCodeLanguage();
      const codeLanguageIndex = codeLanguages.findIndex(lang => lang === curCodeLanguage) + 1;
      
      const allWindows = BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        if (!window.isDestroyed()) {
          try {
            // 发送模型变更事件
            window.webContents.send("model-changed", {
              model: curModel,
              index: modelIndex,
              total: models.length
            });
            
            // 发送编程语言变更事件
            window.webContents.send("code-language-changed", {
              codeLanguage: curCodeLanguage,
              index: codeLanguageIndex,
              total: codeLanguages.length
            });
            
            // 发送配置更新事件
            window.webContents.send("config-updated", {
              success: true,
              message: '配置初始化成功'
            });
            console.log('✅ 配置更新事件已发送到窗口');
          } catch (sendError) {
            console.error('❌ 发送配置更新事件失败:', sendError);
          }
        }
      });
    }

    return result;
  } catch (error) {
    console.error('IPC 配置初始化失败:', error);
    return {
      success: false,
      message: '配置初始化失败',
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
});

// 添加 IPC 处理器用于检查麦克风按钮显示状态
ipcMain.handle('should-show-microphone-button', async (event: IpcMainInvokeEvent) => {
  try {
    const { shouldShowMicrophoneButton } = await import("./handlers/problemHandler");
    return await shouldShowMicrophoneButton();
  } catch (error) {
    console.error('检查麦克风按钮显示状态失败:', error);
    return false;
  }
});
