// electron.d.ts - Type declarations for Electron API

interface ElectronAPI {
  updateContentDimensions: (dimensions: {
    width: number
    height: number
  }) => Promise<void>
  getApiKey: () => Promise<string | null>
  getCurrentModel: () => Promise<{
    model: string
    index: number
    total: number
  }>
  getCurrentCodeLanguage: () => Promise<{
    codeLanguage: string
    index: number
    total: number
  }>
  getScreenshots: () => Promise<{
    success: boolean
    previews?: Array<{ path: string; preview: string }> | null
    error?: string
  }>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => () => void
  onSolutionsReady: (callback: (solutions: string) => void) => () => void
  onResetView: (callback: () => void) => () => void
  onSolutionStart: (callback: () => void) => () => void
  onDebugStart: (callback: () => void) => () => void
  onDebugSuccess: (callback: (data: any) => void) => () => void
  onSolutionError: (callback: (error: string) => void) => () => void
  onProcessingNoScreenshots: (callback: () => void) => () => void
  onConfigInitFailed: (callback: (data: { message: string, detail: string }) => void) => () => void
  onConfigUpdated: (callback: (data: { success: boolean, message: string }) => void) => () => void
  onProblemExtracted: (callback: (data: any) => void) => () => void
  onSolutionSuccess: (callback: (data: any) => void) => () => void
  onFirstPointRecorded: (callback: () => void) => () => void
  onUnauthorized: (callback: () => void) => () => void
  onApiKeyOutOfCredits: (callback: () => void) => () => void
  onDebugError: (callback: (error: string) => void) => () => void
  onCopySolutionContent: (callback: () => void) => () => void
  onModelChanged: (callback: (event: any, data: any) => void) => () => void
  onCodeLanguageChanged: (callback: (event: any, data: any) => void) => () => void
  onWindowMoved: (callback: () => void) => () => void
  takeScreenshot: () => Promise<void>
  takeFullScreenshot: () => Promise<void>
  generateSolution: () => Promise<void>
  moveWindowLeft: () => Promise<void>
  moveWindowRight: () => Promise<void>
  updateApiKey: (apiKey: string) => Promise<void>
  setApiKey: (apiKey: string) => Promise<{ success: boolean }>
  openExternal: (url: string) => void
  // Add our new toolbar bounds update function
  updateToolbarBounds: (bounds: { 
    x: number; 
    y: number; 
    width: number; 
    height: number 
  }) => Promise<{ success: boolean; error?: string }>
  // Add our new toolbar regions update function
  updateToolbarRegions: (regions: Array<{
    x: number;
    y: number;
    width: number;
    height: number;
    type: 'model' | 'code-language' | 'reset' | 'capture' | 'solution' | 'other' | 'voice' | 'voice-back' | 'voice-microphone' | 'voice-system-audio' | 'voice-one-click-start' | 'voice-send-to-ai' | 'ai-response-scroll-up' | 'ai-response-scroll-down' | 'ai-response-fast-scroll-up' | 'ai-response-fast-scroll-down' | 'ai-response-accurate-scroll-up' | 'ai-response-accurate-scroll-down';
  }>) => Promise<{ success: boolean; error?: string }>
  // 通用语音处理方法
  processVoiceToText: (text: string) => Promise<{ 
    success: boolean;
    response?: string;
    error?: string;
  }>
  // 极速模式处理方法
  processFastVoiceText: (text: string) => Promise<{ 
    success: boolean;
    response?: string;
    error?: string;
  }>
  // 精确模式处理方法
  processAccurateVoiceText: (text: string) => Promise<{ 
    success: boolean;
    response?: string;
    error?: string;
  }>
  // 通用 IPC 方法
  invoke: (channel: string, ...args: any[]) => Promise<any>
  on: (channel: string, callback: (...args: any[]) => void) => void
  off: (channel: string, callback: (...args: any[]) => void) => void
  // 系统音频捕获相关API
  startSystemAudioCapturing: () => Promise<{ success: boolean, error?: string }>
  stopSystemAudioCapturing: () => Promise<{ success: boolean, error?: string }>
  getSystemAudioStatus: () => Promise<{ capturing: boolean }>
  onSystemAudioStatus: (callback: (event: any, data: { recording: boolean }) => void) => () => void
  // Voice panel button event listeners
  onVoiceBackClicked: (callback: () => void) => () => void
  onVoiceMicrophoneClicked: (callback: () => void) => () => void
  onVoiceSystemAudioClicked: (callback: () => void) => () => void
  onVoiceOneClickStartClicked: (callback: () => void) => () => void
  onVoiceSendToAIClicked: (callback: () => void) => () => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

export { }

