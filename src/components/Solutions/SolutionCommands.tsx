import React, { useEffect, useRef, useState } from "react"
import ToolbarPositionReporter from "../Common/ToolbarPositionReporter"

// 添加 ModelInfo 接口定义
interface ModelInfo {
  name: string
  index?: number
  total?: number
}

// 添加 CodeLanguageInfo 接口定义
interface CodeLanguageInfo {
  name: string
  index?: number
  total?: number
}

interface ExtraScreenshotsQueueCommandsProps {
  onTooltipVisibilityChange?: (visible: boolean, height: number) => void
  screenshotStatus?: 'idle' | 'first-point' | 'with-screenshot'
  errorState?: boolean
  setView?: React.Dispatch<React.SetStateAction<"queue" | "solutions" | "debug" | "voice">>
  currentView?: "queue" | "solutions" | "debug" | "voice"
}

const ExtraScreenshotsQueueCommands: React.FC<
  ExtraScreenshotsQueueCommandsProps
> = ({ onTooltipVisibilityChange, screenshotStatus = 'idle', errorState = false, setView, currentView }) => {
  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const tooltipRef = useRef<HTMLDivElement>(null)
  // 添加 modelInfo 状态
  const [modelInfo, setModelInfo] = useState<ModelInfo | null>(null)
  // 添加 codeLanguageInfo 状态
  const [codeLanguageInfo, setCodeLanguageInfo] = useState<CodeLanguageInfo | null>(null)
  // State to track local errors
  const [localErrorState, setLocalErrorState] = useState(false)
  
  // 添加语音助手状态
  const [voiceAssistantActive, setVoiceAssistantActive] = useState(false)
  
  // 添加麦克风按钮显示状态
  const [showMicrophoneButton, setShowMicrophoneButton] = useState(false)
  
  // Combine passed error state with local error state
  const combinedErrorState = errorState || localErrorState

  // 添加获取当前模型信息的 useEffect
  useEffect(() => {
    const fetchCurrentModel = async () => {
      try {
        // 通过 IPC 获取当前模型信息
        await window.electronAPI.getCurrentModel()
        setLocalErrorState(false)
      } catch (error) {
        console.error("Failed to fetch current model:", error)
        setLocalErrorState(true)
      }
    }

    const fetchCurrentCodeLanguage = async () => {
      try {
        // 通过 IPC 获取当前编程语言信息
        await window.electronAPI.getCurrentCodeLanguage()
        setLocalErrorState(false)
      } catch (error) {
        console.error("Failed to fetch current code language:", error)
        setLocalErrorState(true)
      }
    }

    const checkVoiceConfig = async () => {
      try {
        // 检查语音功能是否启用
        const shouldShow = await window.electronAPI.shouldShowMicrophoneButton()
        const previousState = showMicrophoneButton;
        setShowMicrophoneButton(shouldShow)
        console.log('麦克风按钮显示状态:', shouldShow)

        // 如果按钮状态发生变化，记录日志但不强制重新渲染
        if (previousState !== shouldShow) {
          console.log('🔄 麦克风按钮状态变化，状态已更新');
        }
      } catch (error) {
        console.error("Failed to check voice config:", error)
        setShowMicrophoneButton(false) // 默认不显示
      }
    }

    fetchCurrentModel()
    fetchCurrentCodeLanguage()
    checkVoiceConfig()
  }, [])

  // 当currentView改变时更新语音助手状态
  useEffect(() => {
    setVoiceAssistantActive(currentView === 'voice');
  }, [currentView]);

  // 监听视图变化，当切换到 solutions 视图时重新检查麦克风按钮状态
  useEffect(() => {
    if (currentView === 'solutions') {
      console.log('🔄 切换到 Solutions 视图，重新检查麦克风按钮状态');

      // 简化状态检查逻辑，避免抖动
      const checkMicrophoneState = async () => {
        try {
          const shouldShow = await window.electronAPI.shouldShowMicrophoneButton();
          console.log('🔄 准备更新麦克风按钮状态:', { 新状态: shouldShow });
          
          // 使用函数式更新避免闭包问题
          setShowMicrophoneButton(prev => {
            if (prev !== shouldShow) {
              console.log('🔄 麦克风按钮状态已更新:', { 从: prev, 到: shouldShow });
              return shouldShow;
            } else {
              console.log('🔄 麦克风按钮状态无需更改');
              return prev;
            }
          });
        } catch (error) {
          console.error('🔄 检查麦克风按钮状态失败:', error);
          setShowMicrophoneButton(false);
        }
      };

      // 只进行一次检查，避免多次触发
      const timer = setTimeout(() => checkMicrophoneState(), 150);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [currentView]) // 移除 showMicrophoneButton 依赖，避免循环依赖

  useEffect(() => {
    if (onTooltipVisibilityChange) {
      let tooltipHeight = 0
      if (tooltipRef.current && isTooltipVisible) {
        tooltipHeight = tooltipRef.current.offsetHeight + 10 // Adjust if necessary
      }
      onTooltipVisibilityChange(isTooltipVisible, tooltipHeight)
    }

    // 添加 modelChanged 事件监听
    const modelChangedCleanup = window.electronAPI.onModelChanged((_, data) => {
      setModelInfo({
        name: data.model || 'Unknown',
        index: data.index,
        total: data.total
      })
      setLocalErrorState(false)
    })

    // 添加 codeLanguageChanged 事件监听
    const codeLanguageChangedCleanup = window.electronAPI.onCodeLanguageChanged((_, data) => {
      setCodeLanguageInfo({
        name: data.codeLanguage || 'Unknown',
        index: data.index,
        total: data.total
      })
      setLocalErrorState(false)
    })

    // Add more error event listeners
    const cleanupFunctions = [
      modelChangedCleanup,
      codeLanguageChangedCleanup,
      window.electronAPI.onSolutionError(() => {
        setLocalErrorState(true)
      }),
      window.electronAPI.onDebugError(() => {
        setLocalErrorState(true)
      }),
      window.electronAPI.onResetView(() => {
        setLocalErrorState(false)
      }),
      window.electronAPI.onConfigUpdated(async (data) => {
        console.log('收到配置更新事件:', data);
        if (data.success) {
          // 配置更新成功，重新检查麦克风按钮显示状态
          try {
            const previousState = showMicrophoneButton;
            const shouldShow = await window.electronAPI.shouldShowMicrophoneButton();
            setShowMicrophoneButton(shouldShow);
            console.log('麦克风按钮显示状态已更新:', shouldShow);

            // 如果按钮状态发生变化，记录日志但不强制重新渲染
            if (previousState !== shouldShow) {
              console.log('🔄 配置更新导致麦克风按钮状态变化，状态已更新');
            }
          } catch (error) {
            console.error('更新麦克风按钮状态失败:', error);
          }
        }
      })
    ];

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    }
  }, [isTooltipVisible, onTooltipVisibilityChange])

  const handleMouseEnter = () => {
    setIsTooltipVisible(true)
  }

  const handleMouseLeave = () => {
    setIsTooltipVisible(false)
  }

  // 添加语音助手切换方法，与 Queue.tsx 保持一致
  const toggleVoiceAssistant = () => {
    console.log('Navigating to Voice view');
    if (setView) {
      setView('voice');
    }
  }

  // 添加 modelInfo 渲染函数
  const renderModelInfo = () => {
    if (modelInfo) {
      let displayText = modelInfo.name;
      if (modelInfo.index && modelInfo.total) {
        displayText += ` (${modelInfo.index}/${modelInfo.total})`;
      }
      return displayText;
    }
    return 'Loading model...';
  }

  // 添加 codeLanguageInfo 渲染函数
  const renderCodeLanguageInfo = () => {
    if (codeLanguageInfo) {
      let displayText = codeLanguageInfo.name;
      if (codeLanguageInfo.index && codeLanguageInfo.total) {
        displayText += ` (${codeLanguageInfo.index}/${codeLanguageInfo.total})`;
      }
      return displayText;
    }
    return 'Loading...';
  }

  // 添加截图状态显示函数
  const getStatusText = () => {
    switch (screenshotStatus) {
      case 'first-point':
        return "▛"
      case 'with-screenshot':
        return "▛▟"
      default:
        return ""
    }
  }

  return (
    <ToolbarPositionReporter
      modelSelector=".model-info"
      codeLanguageSelector=".code-language-info"
      resetSelector=".reset-button"
      captureSelector=".capture-area"
      solutionSelector=".solution-button"
      voiceSelector=".voice-assistant"
      errorState={combinedErrorState}
      currentView={currentView}
    >
      <div key={`toolbar-${showMicrophoneButton}`}>
        <div className="pt-2 w-fit max-w-[85vw] transition-all duration-300">
          <div className="text-xs text-white/95 backdrop-blur-lg bg-black/70 rounded-xl py-2.5 px-5 flex items-center shadow-lg border border-white/10 transition-all duration-200 w-fit max-w-full">
            <div className="flex items-center gap-4 flex-shrink-0">
              {/* Capture (previously Show/Hide) */}
              <div
                className="flex items-center gap-2 cursor-pointer capture-area hover:text-white transition-colors duration-200 group flex-shrink-0"
              >
                <span className="flex items-center justify-center text-white/90 group-hover:text-white transition-colors duration-200 w-4 h-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="w-full h-full"
                  >
                    <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z" />
                    <circle cx="12" cy="13" r="3" />
                  </svg>
                </span>
                <span className="text-[11px] leading-none font-medium my-auto">Capture</span>
              </div>
              <div className="h-4 w-px bg-white/20 flex-shrink-0" />
              {/* Start Over */}
              <div className="flex items-center gap-2 reset-button group flex-shrink-0">
                <span className="text-[11px] leading-none font-medium group-hover:text-white transition-colors duration-200">Reset</span>
                <div className="flex gap-1">
                  <div className="bg-white/15 group-hover:bg-white/25 transition-colors duration-200 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/80 shadow-sm">
                    ⌘
                  </div>
                  <div className="bg-white/15 group-hover:bg-white/25 transition-colors duration-200 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/80 shadow-sm">
                    R
                  </div>
                </div>
              </div>
              <div className="h-4 w-px bg-white/20 flex-shrink-0" />
              <div className="flex items-center flex-shrink-0">
                <span className="text-[11px] leading-none text-yellow-300 font-medium flex items-center">
                  {getStatusText()}
                </span>
              </div>
              <div className="h-4 w-px bg-white/20 flex-shrink-0" />
            </div>
            
            {/* Model Info - 完整显示 */}
            <div className="flex items-center model-info flex-shrink-0 mx-2">
              <span className="text-[10px] leading-none text-green-400 font-medium whitespace-nowrap" title={modelInfo?.name || 'Loading model...'}>
                {renderModelInfo()}
              </span>
            </div>

            <div className="h-4 w-px bg-white/20 flex-shrink-0" />

            {/* Code Language Info - 完整显示 */}
            <div className="flex items-center code-language-info flex-shrink-0 mx-2">
              <span className="text-[10px] leading-none text-blue-400 font-medium whitespace-nowrap" title={codeLanguageInfo?.name || 'Loading language...'}>
                {renderCodeLanguageInfo()}
              </span>
            </div>
            
            {/* 右侧按钮区域 - 使用 ml-auto 确保始终在右侧，不被挤压 */}
            <div className="flex items-center gap-2 flex-shrink-0 ml-auto mr-2">
              <div className="h-4 w-px bg-white/20" />
              {/* Solution button */}
              <div
                className="flex items-center cursor-pointer solution-button hover:text-white transition-colors duration-200 group ml-2"
              >
                <span className="flex items-center font-medium whitespace-nowrap">
                  <span className="flex items-center text-[11px] leading-none mr-1 group-hover:scale-110 transition-transform duration-200">🚀</span>
                  <span className="text-[11px] leading-none">Solution</span>
                </span>
              </div>
              {/* Voice Assistant Button - 条件显示 */}
              {showMicrophoneButton && (
                <>
                  <div className="h-4 w-px bg-white/20 flex-shrink-0 animate-in fade-in duration-300" />
                  <div
                    className={`flex items-center gap-2 cursor-pointer voice-assistant hover:text-white transition-all duration-200 group flex-shrink-0 ml-1 mr-1 animate-in slide-in-from-right-2 fade-in duration-300 ${voiceAssistantActive ? 'bg-blue-500/30 px-2 py-1 rounded-md' : ''}`}
                    onClick={toggleVoiceAssistant}
                  >
                    <span className="flex items-center justify-center text-white/90 group-hover:text-white transition-colors duration-200 w-4 h-4">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="w-full h-full"
                      >
                        <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                        <line x1="12" x2="12" y1="19" y2="22"></line>
                      </svg>
                    </span>
                    <span className="text-[11px] leading-none font-medium my-auto">语音</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </ToolbarPositionReporter>
  )
}

export default ExtraScreenshotsQueueCommands
