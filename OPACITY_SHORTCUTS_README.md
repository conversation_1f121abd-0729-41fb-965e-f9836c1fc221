# 窗口透明度快捷键功能

## 功能说明

为 WindowHelper.ts 添加了窗口透明度控制功能，支持通过快捷键调整窗口的透明度。

## 快捷键

- **Ctrl + <** (Ctrl + Shift + ,): 减小窗口透明度
- **Ctrl + >** (Ctrl + Shift + .): 增大窗口透明度

## 实现细节

### 1. WindowHelper.ts 新增方法

- `decreaseOpacity()`: 减小窗口透明度，每次减少 0.1，最小值为 0.1
- `increaseOpacity()`: 增大窗口透明度，每次增加 0.1，最大值为 1.0

### 2. AppState (main.ts) 新增方法

- `decreaseOpacity()`: 调用 WindowHelper 的 decreaseOpacity 方法
- `increaseOpacity()`: 调用 WindowHelper 的 increaseOpacity 方法

### 3. ShortcutsHelper (shortcuts.ts) 新增快捷键

- 注册了 `CommandOrControl+Shift+,` 和 `CommandOrControl+Shift+.` 快捷键
- 添加了系统级键盘拦截支持
- 只在窗口可见时响应快捷键

## 使用方法

1. 确保应用窗口可见
2. 按 `Ctrl + Shift + ,` 减小透明度
3. 按 `Ctrl + Shift + .` 增大透明度
4. 透明度变化会在控制台输出日志

## 技术特点

- 透明度范围：0.1 - 1.0
- 每次调整步长：0.1
- 支持跨平台（Windows/macOS/Linux）
- 包含完整的错误检查和边界处理
- 集成到现有的快捷键系统中
