
> secure-kernel@1.1.8 dev
> cross-env NODE_ENV=development vite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  VITE v5.4.19  ready in 512 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
vite v5.4.19 building for development...

watching for file changes...
vite v5.4.19 building for development...

watching for file changes...

build started...

build started...
transforming...
transforming...
✓ 1 modules transformed.
rendering chunks...
computing gzip size...
dist-electron/preload.js  14.49 kB │ gzip: 2.37 kB
built in 70ms.
✓ 778 modules transformed.
rendering chunks...
[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/store.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/MouseTrackingHelper.ts, /Users/<USER>/Desktop/coder-master/electron/WindowHelper.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneASRManager.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/SystemASRManager.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/problemHandler.ts, /Users/<USER>/Desktop/coder-master/electron/ipcHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneASRManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneManager.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/microphoneHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/SystemASRManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/handlers/SystemAudioManager.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/SystemAudioManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/systemAudioHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/problemHandler.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ProcessingHelper.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

computing gzip size...
dist-electron/privilege-CKTa1H_Z.js      1.87 kB │ gzip:   0.68 kB
dist-electron/main.js                1,225.04 kB │ gzip: 253.39 kB
built in 1510ms.
从配置中读取模型列表: [
  'doubao-pro',
  'doubao-thinking',
  'deepseek-v3.1',
  'deepseek-r1',
  'claude',
  'grok-4',
  'gpt-5-chat',
  'gemini-2.5-pro'
]
WebSocket module loaded successfully for MicrophoneASR
MicrophoneASRManager instance created
MicrophoneASR: 静音检测状态已重置
MicrophoneASR: 静音检测已禁用 - 阈值=0.001, 最小持续时间=1000ms
MicrophoneManager: 初始化麦克风管理器
WebSocket module loaded successfully for SystemASR
SystemASRManager instance created
Setting up app.whenReady() handler...
Main.ts loaded, app state: { isReady: false, version: '29.4.6' }
App 'ready' event fired
Electron app is ready, starting initialization...
initializeApp: Starting app initialization...
initializeApp: Creating app state...
所有快捷键已注销
快捷键 Alt+C 注册成功，已阻止事件传播
快捷键 Alt+V 注册成功，已阻止事件传播
快捷键 Alt+A 注册成功，已阻止事件传播
快捷键 CommandOrControl+Left 注册成功，已阻止事件传播
快捷键 CommandOrControl+Right 注册成功，已阻止事件传播
快捷键 CommandOrControl+Down 注册成功，已阻止事件传播
快捷键 CommandOrControl+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+B 注册成功，已阻止事件传播
快捷键 CommandOrControl+Shift+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+Shift+Down 注册成功，已阻止事件传播
快捷键 Alt+Z 注册成功，已阻止事件传播
快捷键 Alt+2 注册成功，已阻止事件传播
快捷键 Alt+4 注册成功，已阻止事件传播
快捷键 Alt+3 注册成功，已阻止事件传播
快捷键 Alt+1 注册成功，已阻止事件传播
快捷键 CommandOrControl+Q 注册成功，已阻止事件传播
所有快捷键注册成功
键盘钩子已激活
设置 macOS 键盘钩子
注册系统级键盘监听器
添加键盘钩子: Alt+C
添加键盘钩子: Alt+Z
添加键盘钩子: Alt+1
添加键盘钩子: Alt+2
添加键盘钩子: Alt+3
添加键盘钩子: Alt+V
添加键盘钩子: Alt+A
添加键盘钩子: CommandOrControl+R
添加键盘钩子: CommandOrControl+Left
添加键盘钩子: CommandOrControl+Right
添加键盘钩子: CommandOrControl+Down
添加键盘钩子: CommandOrControl+Up
添加键盘钩子: CommandOrControl+B
添加键盘钩子: CommandOrControl+Q
添加键盘钩子: CommandOrControl+Shift+Up
添加键盘钩子: CommandOrControl+Shift+Down
系统级键盘拦截已设置
ShortcutsHelper initialized
鼠标点击事件监听设置完成
ioHook started
initializeApp: Creating main window...
2025-08-25 17:16:42.580 Electron[8592:26894506] NSWindow does not support nonactivating panel styleMask 0x80
2025-08-25 17:16:42.632 Electron[8592:26894506] NSWindow does not support nonactivating panel styleMask 0x80
初始化SystemAudioManager...
初始化MicrophoneManager...
注册语音处理IPC处理器...
注册语音助手 IPC 处理程序...
initializeApp: Initializing IPC handlers...
initializeApp: 注册全局快捷键...
重新注册全局快捷键
快捷键 Alt+C 已注销
快捷键 Alt+V 已注销
快捷键 Alt+A 已注销
快捷键 CommandOrControl+Left 已注销
快捷键 CommandOrControl+Right 已注销
快捷键 CommandOrControl+Down 已注销
快捷键 CommandOrControl+Up 已注销
快捷键 CommandOrControl+B 已注销
快捷键 CommandOrControl+Shift+Up 已注销
快捷键 CommandOrControl+Shift+Down 已注销
快捷键 Alt+Z 已注销
快捷键 Alt+2 已注销
快捷键 Alt+4 已注销
快捷键 Alt+3 已注销
快捷键 Alt+1 已注销
快捷键 CommandOrControl+Q 已注销
所有快捷键已注销
快捷键 Alt+C 注册成功，已阻止事件传播
快捷键 Alt+V 注册成功，已阻止事件传播
快捷键 Alt+A 注册成功，已阻止事件传播
快捷键 CommandOrControl+Left 注册成功，已阻止事件传播
快捷键 CommandOrControl+Right 注册成功，已阻止事件传播
快捷键 CommandOrControl+Down 注册成功，已阻止事件传播
快捷键 CommandOrControl+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+B 注册成功，已阻止事件传播
快捷键 CommandOrControl+Shift+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+Shift+Down 注册成功，已阻止事件传播
快捷键 Alt+Z 注册成功，已阻止事件传播
快捷键 Alt+2 注册成功，已阻止事件传播
快捷键 Alt+4 注册成功，已阻止事件传播
快捷键 Alt+3 注册成功，已阻止事件传播
快捷键 Alt+1 注册成功，已阻止事件传播
快捷键 CommandOrControl+Q 注册成功，已阻止事件传播
所有快捷键注册成功
initializeApp: 等待窗口加载完成后检查配置...
initializeApp: 窗口正在加载，等待加载完成...
SystemAudioManager: 主窗口已设置
注册系统音频IPC处理器...
SystemAudioManager: 主窗口已设置
移除已存在的system-audio:start-capturing处理器
移除已存在的system-audio:stop-capturing处理器
移除已存在的system-audio:get-status处理器
系统音频相关的IPC处理器已注册
MicrophoneManager: 主窗口已设置
注册麦克风IPC处理器...
MicrophoneManager: 主窗口已设置
移除已存在的microphone:start-capturing处理器
移除已存在的microphone:stop-capturing处理器
移除已存在的microphone:get-status处理器
移除已存在的microphone:process-audio处理器
麦克风相关的IPC处理器已注册
2025-08-25 17:16:42.989 Electron[8592:26894506] +[IMKClient subclass]: chose IMKClient_Modern
2025-08-25 17:16:42.989 Electron[8592:26894506] +[IMKInputSession subclass]: chose IMKInputSession_Modern
initializeApp: 窗口加载完成，开始配置检查
Content dimensions: 512x278, Final window size: 528x278, extraPadding: 16
Content dimensions: 512x278, Final window size: 528x278, extraPadding: 16
Content dimensions: 512x278, Final window size: 528x278, extraPadding: 16
Content dimensions: 525x278, Final window size: 541x278, extraPadding: 16
从配置中读取编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'Typescript',
  'Kotlin',  'Swift',
  'Rust'
]
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3.1',
      'deepseek-r1',
      'claude',
      'grok-4',
      'gpt-5-chat',
      'gemini-2.5-pro'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Content dimensions: 528x73, Final window size: 544x73, extraPadding: 16
从配置中读取编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'Typescript',
  'Kotlin',  'Swift',
  'Rust'
]
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3.1',
      'deepseek-r1',
      'claude',
      'grok-4',
      'gpt-5-chat',
      'gemini-2.5-pro'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Content dimensions: 528x73, Final window size: 544x73, extraPadding: 16
Content dimensions: 541x73, Final window size: 557x73, extraPadding: 16
Content dimensions: 541x73, Final window size: 557x73, extraPadding: 16
Content dimensions: 541x73, Final window size: 557x73, extraPadding: 16
Content dimensions: 541x73, Final window size: 557x73, extraPadding: 16
Content dimensions: 541x73, Final window size: 557x73, extraPadding: 16
Content dimensions: 557x73, Final window size: 573x73, extraPadding: 16
Content dimensions: 557x73, Final window size: 573x73, extraPadding: 16
Content dimensions: 573x73, Final window size: 589x73, extraPadding: 16
Content dimensions: 589x73, Final window size: 605x73, extraPadding: 16
工具条位置已更新: { x: 16, y: 12, width: 557, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 257, y: 24, width: 81, height: 10 },
    { type: 'code-language', x: 355, y: 24, width: 51, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 447, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 526, y: 32, width: 46, height: 16 }
  ]
}
Content dimensions: 589x73, Final window size: 605x73, extraPadding: 16
Content dimensions: 605x73, Final window size: 621x73, extraPadding: 16
Content dimensions: 605x73, Final window size: 621x73, extraPadding: 16
Content dimensions: 621x73, Final window size: 637x73, extraPadding: 16
Content dimensions: 637x73, Final window size: 653x73, extraPadding: 16
Content dimensions: 653x73, Final window size: 669x73, extraPadding: 16
Content dimensions: 669x73, Final window size: 685x73, extraPadding: 16
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3.1',
      'deepseek-r1',
      'claude',
      'grok-4',
      'gpt-5-chat',
      'gemini-2.5-pro'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Content dimensions: 685x73, Final window size: 701x73, extraPadding: 16
Content dimensions: 701x73, Final window size: 717x73, extraPadding: 16
Content dimensions: 717x73, Final window size: 733x73, extraPadding: 16
Content dimensions: 733x73, Final window size: 749x73, extraPadding: 16
Content dimensions: 749x73, Final window size: 765x73, extraPadding: 16
Content dimensions: 765x73, Final window size: 781x73, extraPadding: 16
Content dimensions: 781x73, Final window size: 797x73, extraPadding: 16
Content dimensions: 797x73, Final window size: 813x73, extraPadding: 16
Content dimensions: 797x73, Final window size: 813x73, extraPadding: 16
Content dimensions: 813x73, Final window size: 829x73, extraPadding: 16
Content dimensions: 829x73, Final window size: 845x73, extraPadding: 16
Content dimensions: 845x73, Final window size: 861x73, extraPadding: 16
Content dimensions: 861x73, Final window size: 877x73, extraPadding: 16
Content dimensions: 877x73, Final window size: 893x73, extraPadding: 16
Content dimensions: 877x73, Final window size: 893x73, extraPadding: 16
Content dimensions: 893x73, Final window size: 909x73, extraPadding: 16
Content dimensions: 909x73, Final window size: 925x73, extraPadding: 16
Content dimensions: 925x73, Final window size: 941x73, extraPadding: 16
Content dimensions: 941x73, Final window size: 957x73, extraPadding: 16
Content dimensions: 957x73, Final window size: 973x73, extraPadding: 16
Content dimensions: 973x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
initializeApp: 开始检查并初始化配置...
checkAndInitializeConfig called
检测到已存储的 API Key，开始初始化配置...
开始初始化配置... sk-1192bc4bdb2e4fa5993198aeac83f010
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
configResult: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3.1',
      'deepseek-r1',
      'claude',
      'grok-4',
      'gpt-5-chat',
      'gemini-2.5-pro'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
配置初始化成功
更新工具条上的模型列表和编程语言...
从配置中读取编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'Typescript',
  'Kotlin',  'Swift',
  'Rust'
]
✅ 模型信息已更新: { model: 'doubao-pro', index: 1, total: 8 }
✅ 编程语言信息已更新: { codeLanguage: 'Java', index: 1, total: 13 }
通知渲染进程更新麦克风按钮状态...
✅ 配置更新事件已发送到渲染进程
initializeApp: 开始请求系统权限...
屏幕录制权限状态: granted
initializeApp: 应用初始化完成
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3.1',
      'deepseek-r1',
      'claude',
      'grok-4',
      'gpt-5-chat',
      'gemini-2.5-pro'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
🖱️ 检测到鼠标点击事件: { x: 375, y: 324, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 375, y: 324 },
  windowBounds: { x: 0, y: 0, width: 982, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 557, height: 49 },
  regionsCount: 6
}
使用渲染进程报告的工具条位置: { x: 16, y: 12, width: 557, height: 49 }
鼠标位置 (375, 324) 不在工具条范围内
工具条位置已更新: { x: 16, y: 12, width: 950, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 257, y: 24, width: 81, height: 10 },
    { type: 'code-language', x: 355, y: 24, width: 51, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 447, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 526, y: 21, width: 46, height: 16 }
  ]
}
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Command/Ctrl + down pressed. Moving window down.
Command/Ctrl + down pressed. Moving window down.
Command/Ctrl + down pressed. Moving window down.
Command/Ctrl + down pressed. Moving window down.
Command/Ctrl + down pressed. Moving window down.
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 257, y: 24, width: 81, height: 10 },
    { type: 'code-language', x: 355, y: 24, width: 51, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 447, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 526, y: 21, width: 46, height: 16 }
  ]
}
🖱️ 检测到鼠标点击事件: { x: 79, y: 192, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 79, y: 192 },
  windowBounds: { x: 0, y: 150, width: 982, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 950, height: 49 },
  regionsCount: 6
}
使用渲染进程报告的工具条位置: { x: 16, y: 162, width: 950, height: 49 }
鼠标位置 (79, 192) 在工具条范围内
点击在 capture 区域内
Capture区域被点击，执行全屏截图
[ScreenshotHelper] 请求区域截图: x=0, y=0, width=1512, height=982
2025-08-25 17:16:49.553 Electron[8592:26894506] NSWindow does not support nonactivating panel styleMask 0x80
[ScreenshotHelper] 截图成功 (189773 字节)
[ScreenshotHelper] 图片大小 0.18MB，无需压缩
设置截图可用状态: true
Content dimensions: 982x119, Final window size: 982x119, extraPadding: 16
Content dimensions: 982x119, Final window size: 982x119, extraPadding: 16
Content dimensions: 982x119, Final window size: 982x119, extraPadding: 16
工具条位置已更新: { x: 16, y: 58, width: 950, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 270, y: 24, width: 81, height: 10 },
    { type: 'code-language', x: 368, y: 24, width: 51, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 460, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 539, y: 21, width: 46, height: 16 }
  ]
}
🖱️ 检测到鼠标点击事件: { x: 149, y: 231, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 149, y: 231 },
  windowBounds: { x: 0, y: 150, width: 982, height: 119 },
  toolbarBounds: { x: 16, y: 58, width: 950, height: 49 },
  regionsCount: 6
}
使用渲染进程报告的工具条位置: { x: 16, y: 208, width: 950, height: 49 }
鼠标位置 (149, 231) 在工具条范围内
点击在 reset 区域内
Command + R pressed. Canceling requests and resetting queues...
Current window position: (0, 150), size: (982x119)
Window is visible, position remains unchanged
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
工具条位置已更新: { x: 16, y: 12, width: 950, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 257, y: 24, width: 81, height: 10 },
    { type: 'code-language', x: 355, y: 24, width: 51, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 447, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 526, y: 21, width: 46, height: 16 }
  ]
}
🖱️ 检测到鼠标点击事件: { x: 568, y: 191, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 568, y: 191 },
  windowBounds: { x: 0, y: 150, width: 982, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 950, height: 49 },
  regionsCount: 6
}
使用渲染进程报告的工具条位置: { x: 16, y: 162, width: 950, height: 49 }
鼠标位置 (568, 191) 在工具条范围内
点击在 voice 区域内
Voice区域被点击，显示语音识别界面
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
工具条位置已更新: { x: 3, y: 1, width: 976, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 9,
  regions: [
    { type: 'voice-back', x: 916, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 208, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 172,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 244,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 276, height: 36 },
    {
      type: 'ai-response-fast-scroll-up',
      x: 569,
      y: 644,
      width: 40,
      height: 40
    },
    {
      type: 'ai-response-fast-scroll-down',
      x: 569,
      y: 692,
      width: 40,
      height: 40
    },
    {
      type: 'ai-response-accurate-scroll-up',
      x: 915,
      y: 644,
      width: 40,
      height: 40
    },
    {
      type: 'ai-response-accurate-scroll-down',
      x: 915,
      y: 692,
      width: 40,
      height: 40
    }
  ]
}
🎤 语音面板按钮区域: [
  { x: 916, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 208, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 172, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 244,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 276, height: 36, type: 'voice-send-to-ai' }
]
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 9,
  regions: [
    { type: 'voice-back', x: 916, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 208, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 172,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 244,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 276, height: 36 },
    {
      type: 'ai-response-fast-scroll-up',
      x: 569,
      y: 644,
      width: 40,
      height: 40
    },
    {
      type: 'ai-response-fast-scroll-down',
      x: 569,
      y: 692,
      width: 40,
      height: 40
    },
    {
      type: 'ai-response-accurate-scroll-up',
      x: 915,
      y: 644,
      width: 40,
      height: 40
    },
    {
      type: 'ai-response-accurate-scroll-down',
      x: 915,
      y: 692,
      width: 40,
      height: 40
    }
  ]
}
🎤 语音面板按钮区域: [
  { x: 916, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 208, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 172, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 244,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 276, height: 36, type: 'voice-send-to-ai' }
]
🖱️ 检测到鼠标点击事件: { x: 257, y: 224, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 257, y: 224 },
  windowBounds: { x: 0, y: 150, width: 982, height: 751 },
  toolbarBounds: { x: 3, y: 1, width: 976, height: 749 },
  regionsCount: 9
}
使用渲染进程报告的工具条位置: { x: 3, y: 151, width: 976, height: 749 }
鼠标位置 (257, 224) 在工具条范围内
点击在 voice-one-click-start 区域内
语音面板一键启动按钮被点击
🚀 执行语音识别流程启动: 系统音频=true, 麦克风=true
📡 步骤1: 启动系统音频ASR连接...
Starting SystemASR session for system audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 语音配置加载成功 {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 系统音频识别启用状态: true
SystemASR: Starting connection attempt (ID: d34fffde-8efa-43eb-9408-6a5803e72271)
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
SystemASR: WebSocket connection established
SystemASR: 发送初始化消息 (bigmodel_async)
连接状态更新: systemASR = true {
  systemASR: true,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
✅ 系统音频ASR连接成功
SystemASR: 构建消息 - 序列号=1, 载荷大小=199, 总大小=211
SystemASR: 发送配置消息，序列号=1, 消息大小=211
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x0',
  serialization: 1,
  compression: 0,
  rawBytes: '11901000000000487b22726573756c74'
}
SystemASR: 初始载荷长度: 76
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 转录文本 {"additions":{"log_id":"20250825171654BE2A087465A1E11EC9B6"}}
📡 启动系统音频捕获...
SystemAudioManager: 开始启动系统音频捕获
SystemAudioManager: 第一步 - 启动SystemASR服务
Starting SystemASR session for system audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 系统音频识别启用状态: true
SystemASR: Active WebSocket connection already exists
SystemAudioManager: SystemASR服务启动成功
SystemAudioManager: 第二步 - 启动系统音频捕获
SystemAudioManager: macOS音频捕获命令: /Users/<USER>/Desktop/coder-master/bin/macos/system-audio-capture --output /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
SystemAudioManager: 启动音频捕获进程... (尝试 1/4)
SystemAudioManager: 音频捕获进程已启动
SystemAudioManager: stdout: {"code":"RECORDING_STARTED","sampleRate":48000,"path":"\/Users\/<USER>\/Library\/Application Support\/secure-kernel\/temp\/system-audio.pcm","timestamp":"2025-08-25T09:16:57Z","format":"PCM 16-bit","channels":2}

SystemAudioManager: 录音已开始，文件保存在: /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
macOS音频参数: 采样率=48000, 通道数=2, 位数=16
SystemAudioManager: 输出文件大小: 11776字节
开始监控PCM文件 (安全版本): /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
采用安全读取机制，避免读写竞争
位置指针管理器已重置
启动音频文件监控，首次读取将在200ms后开始
连接状态更新: systemAudio = true {
  systemASR: true,
  microphoneASR: false,
  systemAudio: true,
  microphone: false
}
✅ 系统音频捕获启动成功
🎤 步骤2: 启动麦克风ASR连接...
🎤 Starting MicrophoneASR session for microphone audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 语音配置加载成功 {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 麦克风音频识别启用状态: true
MicrophoneASR: 静音检测状态已重置
MicrophoneASR: Starting connection attempt (ID: b030c569-287a-4890-9dff-21f22d17e350)
MicrophoneASR: WebSocket connection established
MicrophoneASR: 使用固定采样率 16000Hz 进行配置
MicrophoneASR: 发送初始化消息 (与websocket-record-rtc.js一致)
MicrophoneASR: 发送配置消息，消息大小=304字节
MicrophoneASR: 静音检测状态已重置
连接状态更新: microphoneASR = true {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: false
}
✅ 麦克风ASR连接成功
MicrophoneASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x0',
  serialization: 1,
  compression: 0,
  rawBytes: '11901000000000487b22726573756c74'
}
MicrophoneASR: 初始载荷长度: 76
MicrophoneASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
MicrophoneASR: 转录文本 {"additions":{"log_id":"20250825171657BD53D1F56BD2BBF5E5DE"}}
安全读取: 位置=0, 文件大小=65536, 待读取=65536字节
帧处理: 总数据=65536字节, 完整帧=65536字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65536字节, 缓存完整帧=0字节, 有效帧=65536字节, 新不完整帧=0字节
当前文件位置: 65536字节, 总读取: 0字节
发送音频数据: 10922字节, 5461样本, 能量=510.43, 静音=false, 重复=false
SystemASR: 处理音频数据，长度=10922字节, 采样率=16000Hz
SystemASR: 收到元数据 - 重复=false, 静音=false, 能量=510.42977476652624
SystemASR: 处理有效音频数据 - 长度=10922字节, 质量=normal, RMS=0.0154
安全读取并处理 65536 字节音频数据，新位置: 65536
SystemASR: 构建消息 - 序列号=2, 载荷大小=65, 总大小=77
SystemASR: 发送音频数据，序列号=2, 总大小=77字节
安全读取: 位置=65536, 文件大小=130816, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 130816字节, 总读取: 65536字节
发送音频数据: 10880字节, 5440样本, 能量=0.00, 静音=true, 重复=false
SystemASR: 收到元数据 - 重复=false, 静音=true, 能量=0
SystemASR: 处理有效音频数据 - 长度=10880字节, 质量=weak, RMS=0.0000
安全读取并处理 65280 字节音频数据，新位置: 130816
SystemASR: 构建消息 - 序列号=3, 载荷大小=45, 总大小=57
SystemASR: 发送音频数据，序列号=3, 总大小=57字节
安全读取: 位置=130816, 文件大小=192256, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
检测到静音开始
当前文件位置: 192256字节, 总读取: 130816字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 192256
🎤 启动麦克风捕获...
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 第一步 - 启动MicrophoneASRManager服务
🎤 Starting MicrophoneASR session for microphone audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 麦克风音频识别启用状态: true
✅ MicrophoneASR: Active WebSocket connection already exists
🎤 MicrophoneManager: MicrophoneASRManager启动结果: { success: true }
✅ MicrophoneManager: MicrophoneASRManager服务启动成功
🎤 MicrophoneManager: 第二步 - 启动麦克风音频捕获
📡 MicrophoneManager: 发送麦克风状态到渲染进程
✅ MicrophoneManager: 已通知渲染进程录制开始
✅ MicrophoneManager: 麦克风音频捕获启动成功
连接状态更新: microphone = true {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: true
}
✅ 麦克风捕获启动成功
📡 发送 start-microphone-recognition 事件到渲染进程
🎯 语音识别流程启动结果: 整体=true, 系统音频=true, 麦克风=true
📊 连接状态: {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: true
}
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
安全读取: 位置=192256, 文件大小=265216, 待读取=72960字节
帧处理: 总数据=72960字节, 完整帧=72960字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=72960字节, 缓存完整帧=0字节, 有效帧=72960字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 265216字节, 总读取: 192256字节
发送音频数据: 12160字节, 6080样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 72960 字节音频数据，新位置: 265216
安全读取: 位置=265216, 文件大小=318976, 待读取=53760字节
帧处理: 总数据=53760字节, 完整帧=53760字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=53760字节, 缓存完整帧=0字节, 有效帧=53760字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 318976字节, 总读取: 265216字节
发送音频数据: 8960字节, 4480样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 53760 字节音频数据，新位置: 318976
安全读取: 位置=318976, 文件大小=380416, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 380416字节, 总读取: 318976字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 380416
IPC: 收到启动麦克风音频捕获请求
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 麦克风已在录制中
IPC: 收到启动麦克风音频捕获请求
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 麦克风已在录制中
🎤 macOS 麦克风权限状态: granted
🎤 macOS 麦克风权限状态: granted
安全读取: 位置=380416, 文件大小=441856, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 441856字节, 总读取: 380416字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 441856
安全读取: 位置=441856, 文件大小=499456, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 499456字节, 总读取: 441856字节
发送音频数据: 9600字节, 4800样本, 能量=0.00, 静音=true, 重复=false
SystemASR: 收到元数据 - 重复=false, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 57600 字节音频数据，新位置: 499456
[8670:0825/171700.357100:ERROR:system_services.cc(34)] SetApplicationIsDaemon: Error Domain=NSOSStatusErrorDomain Code=-50 "paramErr: error in user parameter list" (-50)
安全读取: 位置=499456, 文件大小=560896, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 560896字节, 总读取: 499456字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 560896
安全读取: 位置=560896, 文件大小=630016, 待读取=69120字节
帧处理: 总数据=69120字节, 完整帧=69120字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=69120字节, 缓存完整帧=0字节, 有效帧=69120字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 630016字节, 总读取: 560896字节
发送音频数据: 11520字节, 5760样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 69120 字节音频数据，新位置: 630016
安全读取: 位置=630016, 文件大小=699136, 待读取=69120字节
帧处理: 总数据=69120字节, 完整帧=69120字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=69120字节, 缓存完整帧=0字节, 有效帧=69120字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
持续静音，重置音频特征
当前文件位置: 699136字节, 总读取: 630016字节
发送音频数据: 11520字节, 5760样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 69120 字节音频数据，新位置: 699136
安全读取: 位置=699136, 文件大小=764416, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 764416字节, 总读取: 699136字节
发送音频数据: 10880字节, 5440样本, 能量=0.00, 静音=true, 重复=false
SystemASR: 收到元数据 - 重复=false, 静音=true, 能量=0
SystemASR: 处理有效音频数据 - 长度=10880字节, 质量=weak, RMS=0.0000
安全读取并处理 65280 字节音频数据，新位置: 764416
SystemASR: 构建消息 - 序列号=4, 载荷大小=45, 总大小=57
SystemASR: 发送音频数据，序列号=4, 总大小=57字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 2730
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=2730字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=2730字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=764416, 文件大小=818176, 待读取=53760字节
帧处理: 总数据=53760字节, 完整帧=53760字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=53760字节, 缓存完整帧=0字节, 有效帧=53760字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 818176字节, 总读取: 764416字节
发送音频数据: 8960字节, 4480样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 53760 字节音频数据，新位置: 818176
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=818176, 文件大小=898816, 待读取=80640字节
帧处理: 总数据=80640字节, 完整帧=80640字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=80640字节, 缓存完整帧=0字节, 有效帧=80640字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 898816字节, 总读取: 818176字节
发送音频数据: 13440字节, 6720样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 80640 字节音频数据，新位置: 898816
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=898816, 文件大小=960256, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 960256字节, 总读取: 898816字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 960256
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=960256, 文件大小=1017856, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 1017856字节, 总读取: 960256字节
发送音频数据: 9600字节, 4800样本, 能量=0.00, 静音=true, 重复=false
SystemASR: 收到元数据 - 重复=false, 静音=true, 能量=0
SystemASR: 处理有效音频数据 - 长度=9600字节, 质量=weak, RMS=0.0000
安全读取并处理 57600 字节音频数据，新位置: 1017856
SystemASR: 构建消息 - 序列号=5, 载荷大小=44, 总大小=56
SystemASR: 发送音频数据，序列号=5, 总大小=56字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1017856, 文件大小=1083136, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1083136字节, 总读取: 1017856字节
发送音频数据: 10880字节, 5440样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 处理音频数据，长度=10880字节, 采样率=16000Hz
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 65280 字节音频数据，新位置: 1083136
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1083136, 文件大小=1140736, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1140736字节, 总读取: 1083136字节
发送音频数据: 9600字节, 4800样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 57600 字节音频数据，新位置: 1140736
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1140736, 文件大小=1202176, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1202176字节, 总读取: 1140736字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1202176
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1202176, 文件大小=1263616, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1263616字节, 总读取: 1202176字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1263616
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1263616, 文件大小=1325056, 待读取=61440字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
持续静音，重置音频特征
当前文件位置: 1325056字节, 总读取: 1263616字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1325056
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1325056, 文件大小=1390336, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 1390336字节, 总读取: 1325056字节
发送音频数据: 10880字节, 5440样本, 能量=0.00, 静音=true, 重复=false
SystemASR: 收到元数据 - 重复=false, 静音=true, 能量=0
SystemASR: 处理有效音频数据 - 长度=10880字节, 质量=weak, RMS=0.0000
安全读取并处理 65280 字节音频数据，新位置: 1390336
SystemASR: 构建消息 - 序列号=6, 载荷大小=45, 总大小=57
SystemASR: 发送音频数据，序列号=6, 总大小=57字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1390336, 文件大小=1451776, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1451776字节, 总读取: 1390336字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1451776
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1451776, 文件大小=1513216, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1513216字节, 总读取: 1451776字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1513216
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1513216, 文件大小=1574656, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1574656字节, 总读取: 1513216字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1574656
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1574656, 文件大小=1636096, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1636096字节, 总读取: 1574656字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1636096
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1636096, 文件大小=1697536, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1697536字节, 总读取: 1636096字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1697536
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1697536, 文件大小=1758976, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1758976字节, 总读取: 1697536字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1758976
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1758976, 文件大小=1820416, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1820416字节, 总读取: 1758976字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1820416
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1820416, 文件大小=1885696, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 1885696字节, 总读取: 1820416字节
发送音频数据: 10880字节, 5440样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 65280 字节音频数据，新位置: 1885696
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1885696, 文件大小=1947136, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
持续静音，重置音频特征
当前文件位置: 1947136字节, 总读取: 1885696字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 1947136
音频监控统计 - 运行时间: 10.1s, 总读取: 31, 成功: 31, 空读: 0, 错误: 0
数据统计 - 总字节: 1947136, 平均速率: 192748 bytes/s, 不完整帧缓存: 0字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1947136, 文件大小=2008576, 待读取=61440字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2008576字节, 总读取: 1947136字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=false
SystemASR: 收到元数据 - 重复=false, 静音=true, 能量=0
SystemASR: 处理有效音频数据 - 长度=10240字节, 质量=weak, RMS=0.0000
安全读取并处理 61440 字节音频数据，新位置: 2008576
SystemASR: 构建消息 - 序列号=7, 载荷大小=45, 总大小=57
SystemASR: 发送音频数据，序列号=7, 总大小=57字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2008576, 文件大小=2070016, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
当前文件位置: 2070016字节, 总读取: 2008576字节
发送音频数据: 10240字节, 5120样本, 能量=0.00, 静音=true, 重复=true
SystemASR: 处理音频数据，长度=10240字节, 采样率=16000Hz
SystemASR: 收到元数据 - 重复=true, 静音=true, 能量=0
SystemASR: 检测到重复或静音数据，触发保活包检查
安全读取并处理 61440 字节音频数据，新位置: 2070016
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2070016, 文件大小=2127616, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 2127616字节, 总读取: 2070016字节
发送音频数据: 9600字节, 4800样本, 能量=0.00, 静音=true, 重复=false
SystemASR: 收到元数据 - 重复=false, 静音=true, 能量=0
SystemASR: 处理有效音频数据 - 长度=9600字节, 质量=weak, RMS=0.0000
安全读取并处理 57600 字节音频数据，新位置: 2127616
SystemASR: 构建消息 - 序列号=8, 载荷大小=44, 总大小=56
SystemASR: 发送音频数据，序列号=8, 总大小=56字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=5462字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎤 MicrophoneASR: 处理音频数据，长度=8192字节, 采样率=16000Hz
MicrophoneASR: 发送PCM音频数据，长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🖱️ 检测到鼠标点击事件: { x: 259, y: 223, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 259, y: 223 },
  windowBounds: { x: 0, y: 150, width: 982, height: 751 },
  toolbarBounds: { x: 3, y: 1, width: 976, height: 749 },
  regionsCount: 9
}
使用渲染进程报告的工具条位置: { x: 3, y: 151, width: 976, height: 749 }
鼠标位置 (259, 223) 在工具条范围内
点击在 voice-one-click-start 区域内
语音面板一键启动按钮被点击
安全读取: 位置=2127616, 文件大小=2189056, 待读取=61440字节
🛑 收到停止语音识别流程请求: 系统音频=true, 麦克风=true
🔇 停止系统音频服务...
停止系统音频捕获
SystemASR: Stopping ASR session
SystemASR: Stopping session
SystemASR: 发送负包关闭连接
开始终止系统音频捕获进程...
正在终止音频捕获进程 PID: 8648
✅ [2025-08-25T09:17:08.975Z] 进程操作: terminate_start (PID: 8648) - Starting termination process
第一步：尝试优雅终止进程...
✅ [2025-08-25T09:17:08.979Z] 进程操作: graceful_terminate (PID: 8648) - Sending SIGTERM
IPC: 收到停止麦克风音频捕获请求
🛑 MicrophoneManager: 停止麦克风音频捕获
🔇 MicrophoneManager: 通知MicrophoneASRManager停止处理音频
MicrophoneASR: Stopping ASR session
MicrophoneASR: 静音检测状态已重置
MicrophoneASR: Stopping session
MicrophoneASR: 发送负包关闭连接
MicrophoneASR: 发送负包，序列号=-1, 消息大小=8字节
MicrophoneASR: 负包发送成功，等待服务器确认关闭
📡 MicrophoneManager: 发送停止录制状态到渲染进程
✅ MicrophoneManager: 已通知渲染进程录制停止
SystemASR: 构建消息 - 序列号=1, 载荷大小=20, 总大小=32
SystemASR: 发送负包，序列号=1, 消息大小=32字节
SystemASR: 负包发送成功，等待服务器确认关闭
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，将发送到ASR进行保活包处理
安全读取并处理 61440 字节音频数据，新位置: 2189056
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
SystemAudioManager: 音频捕获进程退出，代码: null, 信号: SIGTERM
✅ 进程已优雅退出
✅ [2025-08-25T09:17:08.997Z] 进程操作: graceful_exit (PID: 8648) - Process exited gracefully
✅ [2025-08-25T09:17:08.997Z] 进程操作: cleanup_complete (PID: 8648) - Process reference cleared
已删除系统音频输出文件
连接状态更新: systemAudio = false {
  systemASR: true,
  microphoneASR: true,
  systemAudio: false,
  microphone: true
}
✅ 系统音频捕获已停止
SystemASR: Stopping ASR session
SystemASR: Stopping session
SystemASR: 发送负包关闭连接
连接状态更新: systemASR = false {
  systemASR: false,
  microphoneASR: true,
  systemAudio: false,
  microphone: true
}
✅ 系统音频ASR连接已关闭
🎤 停止麦克风服务...
🛑 MicrophoneManager: 停止麦克风音频捕获
⚠️ MicrophoneManager: 麦克风未在录制中
连接状态更新: microphone = false {
  systemASR: false,
  microphoneASR: true,
  systemAudio: false,
  microphone: false
}
✅ 麦克风捕获已停止
MicrophoneASR: Stopping ASR session
MicrophoneASR: 静音检测状态已重置
MicrophoneASR: Stopping session
MicrophoneASR: 发送负包关闭连接
MicrophoneASR: 发送负包，序列号=-1, 消息大小=8字节
MicrophoneASR: 负包发送成功，等待服务器确认关闭
连接状态更新: microphoneASR = false {
  systemASR: false,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
✅ 麦克风ASR连接已关闭
📊 停止后连接状态: {
  systemASR: false,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
SystemASR: 构建消息 - 序列号=1, 载荷大小=20, 总大小=32
SystemASR: 发送负包，序列号=1, 消息大小=32字节
SystemASR: 负包发送成功，等待服务器确认关闭
Content dimensions: 982x751, Final window size: 982x751, extraPadding: 16
🖱️ 检测到鼠标点击事件: { x: 950, y: 173, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 950, y: 173 },
  windowBounds: { x: 0, y: 150, width: 982, height: 751 },
  toolbarBounds: { x: 3, y: 1, width: 976, height: 749 },
  regionsCount: 9
}
使用渲染进程报告的工具条位置: { x: 3, y: 151, width: 976, height: 749 }
鼠标位置 (950, 173) 在工具条范围内
点击在 voice-back 区域内
语音面板返回按钮被点击
IPC: 设置视图为 queue
从配置中读取编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'Typescript',
  'Kotlin',  'Swift',
  'Rust'
]
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3.1',
      'deepseek-r1',
      'claude',
      'grok-4',
      'gpt-5-chat',
      'gemini-2.5-pro'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
从配置中读取编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'Typescript',
  'Kotlin',  'Swift',
  'Rust'
]
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3.1',
      'deepseek-r1',
      'claude',
      'grok-4',
      'gpt-5-chat',
      'gemini-2.5-pro'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
工具条位置已更新: { x: 16, y: 12, width: 950, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 257, y: 24, width: 81, height: 10 },
    { type: 'code-language', x: 355, y: 24, width: 51, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 447, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 526, y: 29, width: 46, height: 16 }
  ]
}
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3.1',
      'deepseek-r1',
      'claude',
      'grok-4',
      'gpt-5-chat',
      'gemini-2.5-pro'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
Content dimensions: 982x73, Final window size: 982x73, extraPadding: 16
🎤 macOS 麦克风权限状态: granted
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 257, y: 24, width: 81, height: 10 },
    { type: 'code-language', x: 355, y: 24, width: 51, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 447, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 526, y: 21, width: 46, height: 16 }
  ]
}
🖱️ 检测到鼠标点击事件: { x: -1098, y: 1102, button: 1, clicks: 2 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: -1098, y: 1102 },
  windowBounds: { x: 0, y: 150, width: 982, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 950, height: 49 },
  regionsCount: 6
}
使用渲染进程报告的工具条位置: { x: 16, y: 162, width: 950, height: 49 }
鼠标位置 (-1098, 1102) 不在工具条范围内
App 'before-quit' event fired
正在停止鼠标监听程序...
开始停止ioHook...
ioHook事件监听器已移除
ioHook.stop()调用完成
ioHook停止完成
正在清理全局快捷键...
快捷键 Alt+C 已注销
快捷键 Alt+V 已注销
快捷键 Alt+A 已注销
快捷键 CommandOrControl+Left 已注销
快捷键 CommandOrControl+Right 已注销
快捷键 CommandOrControl+Down 已注销
快捷键 CommandOrControl+Up 已注销
快捷键 CommandOrControl+B 已注销
快捷键 CommandOrControl+Shift+Up 已注销
快捷键 CommandOrControl+Shift+Down 已注销
快捷键 Alt+Z 已注销
快捷键 Alt+2 已注销
快捷键 Alt+4 已注销
快捷键 Alt+3 已注销
快捷键 Alt+1 已注销
快捷键 CommandOrControl+Q 已注销
所有快捷键已注销
键盘钩子已停用
正在取消处理请求...
App 'will-quit' event fired
App 'quit' event fired - 开始最终清理
清理 SystemAudioManager...
停止系统音频捕获
系统音频捕获未启动，无需停止
清理 MicrophoneManager...
🛑 MicrophoneManager: 停止麦克风音频捕获
⚠️ MicrophoneManager: 麦克风未在录制中
所有资源清理完成
