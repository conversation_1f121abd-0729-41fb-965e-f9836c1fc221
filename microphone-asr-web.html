<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麦克风实时语音转文字</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .config-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .form-group input {
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .controls-section {
            padding: 30px;
            text-align: center;
        }

        .control-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 120px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background: #c53030;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .status-section {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f7fafc;
            border-radius: 8px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .status-indicator.connected {
            background: #48bb78;
        }

        .status-indicator.disconnected {
            background: #e53e3e;
        }

        .status-indicator.processing {
            background: #ed8936;
        }

        .transcription-section {
            padding: 30px;
        }

        .transcription-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .transcription-display {
            background: #f7fafc;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            font-size: 16px;
            line-height: 1.6;
        }

        .transcription-item {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .transcription-item.final {
            border-left: 4px solid #48bb78;
        }

        .transcription-item.interim {
            border-left: 4px solid #ed8936;
            opacity: 0.8;
        }

        .transcription-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .transcription-text {
            color: #333;
        }

        .audio-visualizer {
            width: 100%;
            height: 60px;
            background: #f7fafc;
            border-radius: 8px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .visualizer-canvas {
            width: 100%;
            height: 100%;
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e53e3e;
        }

        .success-message {
            background: #c6f6d5;
            color: #2f855a;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #48bb78;
        }

        @media (max-width: 768px) {
            .config-grid {
                grid-template-columns: 1fr;
            }

            .control-buttons {
                flex-direction: column;
                align-items: center;
            }

            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 麦克风实时语音转文字</h1>
            <p>基于字节跳动ASR服务的实时语音识别</p>
        </div>

        <div class="config-section">
            <h2 style="margin-bottom: 20px; color: #333;">配置信息</h2>
            <div class="config-grid">
                <div class="form-group">
                    <label for="appId">App ID</label>
                    <input type="text" id="appId" placeholder="请输入 App ID">
                </div>
                <div class="form-group">
                    <label for="accessKey">Access Key</label>
                    <input type="password" id="accessKey" placeholder="请输入 Access Key">
                </div>
            </div>
            <div class="form-group">
                <label for="wsUrl">WebSocket 地址</label>
                <input type="text" id="wsUrl" value="wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async" placeholder="WebSocket 服务地址">
            </div>
        </div>

        <div class="controls-section">
            <div class="control-buttons">
                <button class="btn btn-primary" id="startBtn">🎤 开始录音</button>
                <button class="btn btn-danger" id="stopBtn" disabled>⏹️ 停止录音</button>
                <button class="btn btn-primary" id="clearBtn">🗑️ 清空记录</button>
            </div>
            
            <div class="audio-visualizer">
                <canvas class="visualizer-canvas" id="visualizer"></canvas>
            </div>
        </div>

        <div class="status-section">
            <h3 style="margin-bottom: 15px; color: #333;">系统状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-indicator disconnected" id="connectionStatus"></div>
                    <span>WebSocket连接: <span id="connectionText">未连接</span></span>
                </div>
                <div class="status-item">
                    <div class="status-indicator disconnected" id="microphoneStatus"></div>
                    <span>麦克风状态: <span id="microphoneText">未启用</span></span>
                </div>
                <div class="status-item">
                    <div class="status-indicator disconnected" id="processingStatus"></div>
                    <span>处理状态: <span id="processingText">空闲</span></span>
                </div>
            </div>
        </div>

        <div class="transcription-section">
            <div class="transcription-title">📝 转录结果</div>
            <div class="transcription-display" id="transcriptionDisplay">
                <div style="text-align: center; color: #666; padding: 40px;">
                    点击"开始录音"开始语音转文字...
                </div>
            </div>
        </div>
    </div>

    <script>
        class MicrophoneASRWebClient {
            constructor() {
                this.wsConnection = null;
                this.isConnected = false;
                this.isRecording = false;
                this.mediaRecorder = null;
                this.audioContext = null;
                this.analyser = null;
                this.microphone = null;
                this.processor = null;
                this.sequence = 1;
                this.sessionStarted = false;
                this.readyToSendAudio = false;
                this.messageQueue = [];
                this.lastActivityTimestamp = 0;
                this.heartbeatInterval = null;
                this.pingInterval = null;
                this.lastPongTime = 0;
                this.currentTranscription = '';
                this.lastHistoryText = '';
                this.transcriptionItems = [];

                // 初始化UI
                this.initializeUI();
                this.initializeVisualizer();
            }

            initializeUI() {
                // 绑定按钮事件
                document.getElementById('startBtn').addEventListener('click', () => this.startRecording());
                document.getElementById('stopBtn').addEventListener('click', () => this.stopRecording());
                document.getElementById('clearBtn').addEventListener('click', () => this.clearTranscriptions());

                // 状态更新
                this.updateStatus('connection', false, '未连接');
                this.updateStatus('microphone', false, '未启用');
                this.updateStatus('processing', false, '空闲');
            }

            initializeVisualizer() {
                this.visualizerCanvas = document.getElementById('visualizer');
                this.visualizerCtx = this.visualizerCanvas.getContext('2d');
                this.resizeVisualizer();
                window.addEventListener('resize', () => this.resizeVisualizer());
            }

            resizeVisualizer() {
                const rect = this.visualizerCanvas.getBoundingClientRect();
                this.visualizerCanvas.width = rect.width;
                this.visualizerCanvas.height = rect.height;
            }

            updateStatus(type, connected, text) {
                const indicator = document.getElementById(`${type}Status`);
                const textElement = document.getElementById(`${type}Text`);
                
                indicator.className = `status-indicator ${connected ? 'connected' : 'disconnected'}`;
                if (type === 'processing' && connected) {
                    indicator.className = 'status-indicator processing';
                }
                textElement.textContent = text;
            }

            showMessage(message, type = 'error') {
                const existingMessage = document.querySelector('.error-message, .success-message');
                if (existingMessage) {
                    existingMessage.remove();
                }

                const messageDiv = document.createElement('div');
                messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
                messageDiv.textContent = message;

                const controlsSection = document.querySelector('.controls-section');
                controlsSection.appendChild(messageDiv);

                setTimeout(() => {
                    messageDiv.remove();
                }, 5000);
            }

            async startRecording() {
                try {
                    // 验证配置
                    const appId = document.getElementById('appId').value.trim();
                    const accessKey = document.getElementById('accessKey').value.trim();
                    
                    if (!appId || !accessKey) {
                        this.showMessage('请先填写App ID和Access Key');
                        return;
                    }

                    // 请求麦克风权限
                    const stream = await navigator.mediaDevices.getUserMedia({ 
                        audio: {
                            sampleRate: 44100,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        } 
                    });

                    this.updateStatus('microphone', true, '已启用');

                    // 初始化音频上下文
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.analyser = this.audioContext.createAnalyser();
                    this.analyser.fftSize = 2048;
                    
                    this.microphone = this.audioContext.createMediaStreamSource(stream);
                    this.microphone.connect(this.analyser);

                    // 创建ScriptProcessorNode处理音频
                    this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);
                    this.processor.onaudioprocess = (e) => this.processAudioData(e);
                    this.analyser.connect(this.processor);
                    this.processor.connect(this.audioContext.destination);

                    // 连接WebSocket
                    await this.connectWebSocket();

                    // 开始可视化
                    this.startVisualization();

                    // 更新UI
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                    this.isRecording = true;

                    this.showMessage('录音已开始，正在连接语音识别服务...', 'success');

                } catch (error) {
                    console.error('启动录音失败:', error);
                    this.showMessage(`启动录音失败: ${error.message}`);
                    this.updateStatus('microphone', false, '启动失败');
                }
            }

            async connectWebSocket() {
                const appId = document.getElementById('appId').value.trim();
                const accessKey = document.getElementById('accessKey').value.trim();
                const wsUrl = document.getElementById('wsUrl').value.trim();

                const requestId = this.generateUUID();

                return new Promise((resolve, reject) => {
                    try {
                        this.wsConnection = new WebSocket(wsUrl, [], {
                            headers: {
                                'X-Api-App-Key': appId,
                                'X-Api-Access-Key': accessKey,
                                'X-Api-Resource-Id': 'volc.bigasr.sauc.duration',
                                'X-Api-Request-Id': requestId
                            }
                        });

                        this.wsConnection.binaryType = 'arraybuffer';

                        const connectionTimeout = setTimeout(() => {
                            if (!this.isConnected) {
                                reject(new Error('连接超时'));
                            }
                        }, 15000);

                        this.wsConnection.onopen = () => {
                            clearTimeout(connectionTimeout);
                            console.log('WebSocket连接已建立');
                            this.isConnected = true;
                            this.lastActivityTimestamp = Date.now();
                            this.lastPongTime = Date.now();
                            
                            this.updateStatus('connection', true, '已连接');
                            this.sendInitMessage();
                            this.processQueuedMessages();
                            this.startHeartbeat();
                            
                            resolve();
                        };

                        this.wsConnection.onmessage = (event) => {
                            this.handleWebSocketMessage(event.data);
                        };

                        this.wsConnection.onerror = (error) => {
                            clearTimeout(connectionTimeout);
                            console.error('WebSocket错误:', error);
                            this.updateStatus('connection', false, '连接错误');
                            reject(error);
                        };

                        this.wsConnection.onclose = (event) => {
                            clearTimeout(connectionTimeout);
                            console.log(`WebSocket连接关闭: ${event.code} - ${event.reason}`);
                            this.isConnected = false;
                            this.sessionStarted = false;
                            this.readyToSendAudio = false;
                            this.updateStatus('connection', false, '连接关闭');
                            this.clearIntervals();
                        };

                    } catch (error) {
                        reject(error);
                    }
                });
            }

            sendInitMessage() {
                if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
                    console.error('无法发送初始化消息: WebSocket未连接');
                    return;
                }

                try {
                    const requestConfig = {
                        user: { uid: "microphone-web-client" },
                        audio: {
                            format: "pcm",
                            codec: "raw",
                            rate: 16000,
                            bits: 16,
                            channel: 1,
                        },
                        request: {
                            model_name: "bigmodel",
                            enable_itn: true,
                            enable_punc: true,
                            enable_ddc: true,
                            show_utterances: true,
                            enable_nonstream: false,
                            result_type: 'single',
                            force_to_speech_time: 0,
                        },
                    };

                    const message = this.encodeFullClientRequest(requestConfig);
                    console.log('发送初始化消息，大小:', message.size);
                    
                    this.wsConnection.send(message);
                    this.readyToSendAudio = false;
                    this.lastActivityTimestamp = Date.now();

                } catch (error) {
                    console.error('发送初始化消息失败:', error);
                }
            }

            processAudioData(audioProcessingEvent) {
                if (!this.isRecording || !this.isConnected) return;

                const inputBuffer = audioProcessingEvent.inputBuffer;
                const inputData = inputBuffer.getChannelData(0);
                
                // 转换为PCM16格式
                const pcm16Data = this.convertFloat32ToPCM16(inputData);
                
                // 从44100Hz转换到16000Hz
                const resampledData = this.convertSampleRate(pcm16Data, 44100, 16000);

                // 转换为Uint8Array
                const audioBytes = new Uint8Array(resampledData.buffer);

                const audioData = {
                    audio_data: audioBytes.buffer,
                    sample_rate: 16000,
                    audio_format: 'pcm'
                };

                this.sendAudioData(audioData);
            }

            convertFloat32ToPCM16(float32Array) {
                const pcm16 = new Int16Array(float32Array.length);
                for (let i = 0; i < float32Array.length; i++) {
                    const s = Math.max(-1, Math.min(1, float32Array[i]));
                    pcm16[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
                }
                return pcm16;
            }

            convertSampleRate(audioData, sourceSampleRate, targetSampleRate) {
                if (sourceSampleRate === targetSampleRate) {
                    return audioData;
                }

                const samples = audioData;
                let newSampleCount;

                if (sourceSampleRate > targetSampleRate) {
                    const ratio = sourceSampleRate / targetSampleRate;
                    newSampleCount = Math.floor(samples.length / ratio);
                } else {
                    const ratio = targetSampleRate / sourceSampleRate;
                    newSampleCount = Math.floor(samples.length * ratio);
                }

                const newSamples = new Int16Array(newSampleCount);

                if (sourceSampleRate > targetSampleRate) {
                    const ratio = sourceSampleRate / targetSampleRate;
                    for (let i = 0; i < newSampleCount; i++) {
                        const srcIndex = i * ratio;
                        const srcIndexInt = Math.floor(srcIndex);
                        const srcIndexFrac = srcIndex - srcIndexInt;

                        if (srcIndexInt + 1 < samples.length) {
                            newSamples[i] = Math.round(samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac);
                        } else {
                            newSamples[i] = samples[srcIndexInt];
                        }
                    }
                } else {
                    const ratio = targetSampleRate / sourceSampleRate;
                    for (let i = 0; i < newSampleCount; i++) {
                        const srcIndex = i / ratio;
                        const srcIndexInt = Math.floor(srcIndex);
                        const srcIndexFrac = srcIndex - srcIndexInt;

                        if (srcIndexInt + 1 < samples.length) {
                            newSamples[i] = Math.round(samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac);
                        } else {
                            newSamples[i] = samples[srcIndexInt < samples.length ? srcIndexInt : samples.length - 1];
                        }
                    }
                }

                return newSamples;
            }

            sendAudioData(audioData) {
                if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
                    console.warn('WebSocket未连接，音频数据加入队列');
                    this.messageQueue.push(audioData);
                    return;
                }

                if (!this.sessionStarted) {
                    console.log('会话尚未开始，将音频数据加入队列');
                    this.messageQueue.push(audioData);
                    return;
                }

                if (!this.readyToSendAudio) {
                    console.warn('尚未准备好发送音频，加入队列');
                    this.messageQueue.push(audioData);
                    return;
                }

                try {
                    this.sequence++;
                    const audioBytes = new Uint8Array(audioData.audio_data);
                    
                    if (audioBytes.length === 0) {
                        return;
                    }

                    // 检查是否为完全静音
                    const nonZeroSamples = Array.from(audioBytes).filter(value => value !== 0).length;
                    const percentNonZero = (nonZeroSamples / audioBytes.length) * 100;

                    if (percentNonZero === 0) {
                        return; // 跳过完全静音的数据
                    }

                    const message = this.encodeAudioOnlyRequest(audioBytes.buffer);
                    this.wsConnection.send(message);
                    
                    this.lastActivityTimestamp = Date.now();
                    this.updateStatus('processing', true, '处理中');

                } catch (error) {
                    console.error('发送音频数据失败:', error);
                }
            }

            processQueuedMessages() {
                if (this.messageQueue.length > 0) {
                    console.log(`发送 ${this.messageQueue.length} 个队列消息`);
                    this.messageQueue.forEach(audioData => {
                        this.sendAudioData(audioData);
                    });
                    this.messageQueue = [];
                }
            }

            startHeartbeat() {
                this.clearIntervals();
                
                this.pingInterval = setInterval(() => {
                    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
                        try {
                            // 发送心跳
                            const now = Date.now();
                            if (now - this.lastPongTime > 30000) {
                                console.warn('30秒内未收到pong响应');
                                this.handleConnectionFailure();
                            }
                        } catch (error) {
                            console.error('发送ping失败:', error);
                            this.handleConnectionFailure();
                        }
                    }
                }, 15000);
                
                this.heartbeatInterval = setInterval(() => {
                    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
                        const currentTime = Date.now();
                        if (currentTime - this.lastActivityTimestamp > 15000) {
                            try {
                                this.wsConnection.send(JSON.stringify({ type: 'heartbeat' }));
                                this.lastActivityTimestamp = currentTime;
                            } catch (error) {
                                console.error('发送心跳失败:', error);
                                this.handleConnectionFailure();
                            }
                        }
                    } else {
                        this.clearIntervals();
                    }
                }, 10000);
            }

            clearIntervals() {
                if (this.heartbeatInterval) {
                    clearInterval(this.heartbeatInterval);
                    this.heartbeatInterval = null;
                }
                if (this.pingInterval) {
                    clearInterval(this.pingInterval);
                    this.pingInterval = null;
                }
            }

            handleConnectionFailure() {
                console.log('处理连接失败...');
                
                if (this.wsConnection) {
                    try {
                        this.wsConnection.close();
                    } catch (e) {
                        console.error('关闭连接失败:', e);
                    }
                    this.wsConnection = null;
                }
                
                this.isConnected = false;
                this.clearIntervals();
                this.updateStatus('connection', false, '连接失败');
            }

            handleWebSocketMessage(data) {
                if (data instanceof ArrayBuffer) {
                    this.parseResponse(new Uint8Array(data));
                } else if (typeof data === 'string') {
                    console.error('服务器错误:', data);
                }
            }

            async parseResponse(data) {
                if (!data || data.length === 0) {
                    console.warn('空响应数据');
                    return;
                }

                const msg = data;
                const response = {
                    code: 0,
                    event: 0,
                    is_last_package: false,
                    payload_sequence: 0,
                    payload_size: 0,
                    payload_msg: null,
                };

                const headerSize = msg[0] & 0x0f;
                const messageType = msg[1] >> 4;
                const messageTypeSpecificFlags = msg[1] & 0x0f;
                const serializationMethod = msg[2] >> 4;
                const messageCompression = msg[2] & 0x0f;

                let payload = msg.slice(headerSize * 4);

                // 解析flags
                if (messageTypeSpecificFlags & 0x01) {
                    const dataView = new DataView(payload.buffer, payload.byteOffset);
                    response.payload_sequence = dataView.getInt32(0, false); // big-endian
                    payload = payload.slice(4);
                }
                if (messageTypeSpecificFlags & 0x02) {
                    response.is_last_package = true;
                }
                if (messageTypeSpecificFlags & 0x04) {
                    if (payload.length >= 4) {
                        const dataView = new DataView(payload.buffer, payload.byteOffset);
                        response.event = dataView.getInt32(0, false); // big-endian
                        payload = payload.slice(4);
                    }
                }

                // 解析message_type
                if (messageType === 0x09) { // SERVER_FULL_RESPONSE
                    const dataView = new DataView(payload.buffer, payload.byteOffset);
                    response.payload_size = dataView.getUint32(0, false); // big-endian
                    payload = payload.slice(4);
                } else if (messageType === 0x0f) { // SERVER_ERROR
                    const dataView = new DataView(payload.buffer, payload.byteOffset);
                    response.code = dataView.getInt32(0, false); // big-endian
                    response.payload_size = dataView.getUint32(4, false); // big-endian
                    payload = payload.slice(8);
                }

                if (!payload || payload.length === 0) {
                    await this.handleParsedResponse(response);
                    return;
                }

                // 解压缩 (如果需要)
                if (messageCompression === 0x01) { // GZIP
                    try {
                        // 对于Web环境，可能需要使用第三方库来解压gzip
                        console.warn('收到gzip压缩数据，但Web环境暂不支持解压');
                    } catch (e) {
                        if (response.event !== 150) {
                            console.error('解压缩失败:', e);
                            return;
                        }
                    }
                }

                // 解析payload
                if (serializationMethod === 0x01 && payload.length > 0) { // JSON
                    try {
                        const decoder = new TextDecoder('utf-8');
                        response.payload_msg = JSON.parse(decoder.decode(payload));
                    } catch (e) {
                        if (response.event !== 150) {
                            console.error('JSON解析失败:', e);
                            return;
                        }
                    }
                }

                await this.handleParsedResponse(response);
            }

            async handleParsedResponse(response) {
                console.log('处理响应:', {
                    code: response.code,
                    event: response.event,
                    isLastPackage: response.is_last_package,
                    hasPayload: !!response.payload_msg,
                });

                // 处理事件
                if (response.event === 150) {
                    console.log('服务器握手成功');
                    this.sessionStarted = true;
                    this.readyToSendAudio = true;
                    this.updateStatus('processing', true, '已就绪');
                    return;
                }

                if (response.event === 153) {
                    this.sessionStarted = false;
                    this.readyToSendAudio = false;
                    console.error('服务器连接失败');
                    this.updateStatus('connection', false, '连接失败');
                    return;
                }

                if (response.code !== 0) {
                    this.sessionStarted = false;
                    this.readyToSendAudio = false;
                    console.error('服务器错误响应', { code: response.code });
                    this.updateStatus('processing', false, `错误: ${response.code}`);
                    return;
                }

                // 处理转录结果
                if (response.payload_msg && response.payload_msg.result) {
                    const result = response.payload_msg.result;
                    console.log('收到转录结果:', result);

                    if (typeof result === 'object' && !Array.isArray(result)) {
                        if (result.utterances && Array.isArray(result.utterances)) {
                            this.processUtterances(result.utterances);
                        } else if (result.text) {
                            this.processTranscriptionText(result.text);
                        }
                    } else if (Array.isArray(result)) {
                        for (const item of result) {
                            if (item.text) {
                                this.processTranscriptionText(item.text);
                            }
                        }
                    }
                }
            }

            processUtterances(utterances) {
                for (const utterance of utterances) {
                    if (!utterance.text || !utterance.text.trim()) {
                        continue;
                    }

                    const trimmedText = utterance.text.trim();
                    const isHistoryText = utterance.definite === true;

                    this.addTranscriptionItem({
                        text: trimmedText,
                        isFinal: isHistoryText,
                        timestamp: new Date().toISOString(),
                        startTime: utterance.start_time,
                        endTime: utterance.end_time
                    });
                }
            }

            processTranscriptionText(text) {
                if (!text || !text.trim()) {
                    return;
                }

                const trimmedText = text.trim();
                const currentPrefix = this.currentTranscription.substring(0, 20);
                const newPrefix = trimmedText.substring(0, 20);

                const isContinuousTranscription = currentPrefix.length > 0 && 
                    (newPrefix.startsWith(currentPrefix) || this.isSimilarSimple(currentPrefix, newPrefix));

                if (isContinuousTranscription) {
                    // 正在转录 - 更新当前转录内容
                    this.currentTranscription = trimmedText;
                    this.updateCurrentTranscription(trimmedText);
                } else {
                    // 新的转录内容 - 将之前的内容加入历史记录
                    if (this.currentTranscription && this.currentTranscription !== this.lastHistoryText) {
                        this.addTranscriptionItem({
                            text: this.currentTranscription,
                            isFinal: true,
                            timestamp: new Date().toISOString()
                        });
                        this.lastHistoryText = this.currentTranscription;
                    }

                    // 开始新的转录
                    this.currentTranscription = trimmedText;
                    this.updateCurrentTranscription(trimmedText);
                }
            }

            isSimilarSimple(str1, str2) {
                if (!str1 || !str2) return false;

                const normalize = (str) => {
                    return str
                        .replace(/\s+/g, '')
                        .replace(/[^\w\u4e00-\u9fff]/g, '')
                        .toLowerCase();
                };

                const normalized1 = normalize(str1);
                const normalized2 = normalize(str2);

                if (normalized1 === normalized2) return true;
                if (normalized1.length > 0 && normalized2.startsWith(normalized1)) return true;
                if (normalized2.length > 0 && normalized1.startsWith(normalized2)) return true;

                const set1 = new Set(normalized1);
                const set2 = new Set(normalized2);
                const intersection = new Set([...set1].filter(char => set2.has(char)));
                const union = new Set([...set1, ...set2]);

                const similarity = intersection.size / union.size;
                return similarity >= 0.8;
            }

            updateCurrentTranscription(text) {
                const display = document.getElementById('transcriptionDisplay');
                let currentItem = display.querySelector('.transcription-item.interim:last-child');
                
                if (!currentItem) {
                    currentItem = document.createElement('div');
                    currentItem.className = 'transcription-item interim';
                    
                    const meta = document.createElement('div');
                    meta.className = 'transcription-meta';
                    meta.textContent = `${new Date().toLocaleTimeString()} - 正在转录...`;
                    
                    const textDiv = document.createElement('div');
                    textDiv.className = 'transcription-text';
                    textDiv.textContent = text;
                    
                    currentItem.appendChild(meta);
                    currentItem.appendChild(textDiv);
                    display.appendChild(currentItem);
                } else {
                    const textDiv = currentItem.querySelector('.transcription-text');
                    textDiv.textContent = text;
                }

                display.scrollTop = display.scrollHeight;
            }

            addTranscriptionItem(item) {
                const display = document.getElementById('transcriptionDisplay');
                
                // 移除"开始录音"提示
                const placeholder = display.querySelector('div[style*="text-align: center"]');
                if (placeholder) {
                    placeholder.remove();
                }

                // 如果是最终文本，将当前临时项转换为最终项
                if (item.isFinal) {
                    const currentInterim = display.querySelector('.transcription-item.interim:last-child');
                    if (currentInterim) {
                        currentInterim.className = 'transcription-item final';
                        const meta = currentInterim.querySelector('.transcription-meta');
                        meta.textContent = `${new Date().toLocaleTimeString()} - 已完成`;
                        return;
                    }
                }

                const transcriptionItem = document.createElement('div');
                transcriptionItem.className = `transcription-item ${item.isFinal ? 'final' : 'interim'}`;
                
                const meta = document.createElement('div');
                meta.className = 'transcription-meta';
                meta.textContent = `${new Date().toLocaleTimeString()} - ${item.isFinal ? '已完成' : '正在转录...'}`;
                
                const textDiv = document.createElement('div');
                textDiv.className = 'transcription-text';
                textDiv.textContent = item.text;
                
                transcriptionItem.appendChild(meta);
                transcriptionItem.appendChild(textDiv);
                
                display.appendChild(transcriptionItem);
                display.scrollTop = display.scrollHeight;

                this.transcriptionItems.push(item);
            }

            clearTranscriptions() {
                const display = document.getElementById('transcriptionDisplay');
                display.innerHTML = '<div style="text-align: center; color: #666; padding: 40px;">转录记录已清空</div>';
                this.transcriptionItems = [];
                this.currentTranscription = '';
                this.lastHistoryText = '';
            }

            startVisualization() {
                const draw = () => {
                    if (!this.isRecording || !this.analyser) return;

                    const bufferLength = this.analyser.frequencyBinCount;
                    const dataArray = new Uint8Array(bufferLength);
                    this.analyser.getByteFrequencyData(dataArray);

                    const canvas = this.visualizerCanvas;
                    const ctx = this.visualizerCtx;
                    const width = canvas.width;
                    const height = canvas.height;

                    ctx.clearRect(0, 0, width, height);

                    const barWidth = (width / bufferLength) * 2.5;
                    let barHeight;
                    let x = 0;

                    // 绘制频谱
                    for (let i = 0; i < bufferLength; i++) {
                        barHeight = (dataArray[i] / 255) * height;

                        const gradient = ctx.createLinearGradient(0, height - barHeight, 0, height);
                        gradient.addColorStop(0, '#667eea');
                        gradient.addColorStop(1, '#764ba2');

                        ctx.fillStyle = gradient;
                        ctx.fillRect(x, height - barHeight, barWidth, barHeight);

                        x += barWidth + 1;
                    }

                    requestAnimationFrame(draw);
                };

                draw();
            }

            stopRecording() {
                try {
                    this.isRecording = false;

                    // 停止音频处理
                    if (this.processor) {
                        this.processor.disconnect();
                        this.processor = null;
                    }

                    if (this.microphone) {
                        this.microphone.disconnect();
                        this.microphone = null;
                    }

                    if (this.audioContext) {
                        this.audioContext.close();
                        this.audioContext = null;
                    }

                    // 关闭WebSocket连接
                    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
                        this.sendNegativePacketToClose();
                        setTimeout(() => {
                            if (this.wsConnection) {
                                this.wsConnection.close(1000, '用户停止录音');
                                this.wsConnection = null;
                            }
                        }, 500);
                    }

                    this.clearIntervals();

                    // 更新UI
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    
                    this.updateStatus('microphone', false, '已停止');
                    this.updateStatus('connection', false, '已断开');
                    this.updateStatus('processing', false, '空闲');

                    // 清空可视化器
                    const ctx = this.visualizerCtx;
                    ctx.clearRect(0, 0, this.visualizerCanvas.width, this.visualizerCanvas.height);

                    this.showMessage('录音已停止', 'success');

                } catch (error) {
                    console.error('停止录音失败:', error);
                    this.showMessage(`停止录音失败: ${error.message}`);
                }
            }

            sendNegativePacketToClose() {
                if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
                    return;
                }

                try {
                    console.log('发送结束包...');
                    const emptyAudioData = new ArrayBuffer(0);
                    this.sequence = -Math.abs(this.sequence);
                    const message = this.encodeAudioOnlyRequest(emptyAudioData);
                    this.wsConnection.send(message);
                    this.lastActivityTimestamp = Date.now();
                } catch (error) {
                    console.error('发送结束包失败:', error);
                }
            }

            // 编码方法
            encodeFullClientRequest(requestData) {
                const fullClientRequestHeader = this.generateHeader();
                const json = JSON.stringify(requestData);
                const jsonBytes = new TextEncoder().encode(json);
                
                // 设置payload大小
                const headerView = new DataView(fullClientRequestHeader);
                headerView.setUint32(4, jsonBytes.length, false); // big-endian
                
                return new Blob([fullClientRequestHeader, jsonBytes]);
            }

            encodeAudioOnlyRequest(requestData) {
                const audioOnlyRequestHeader = this.generateHeader(0b0010); // CLIENT_AUDIO_ONLY_REQUEST
                const headerView = new DataView(audioOnlyRequestHeader);
                headerView.setUint32(4, requestData.byteLength, false); // big-endian
                return new Blob([audioOnlyRequestHeader, requestData]);
            }

            generateHeader(
                messageType = 0b0001, // CLIENT_FULL_REQUEST
                version = 0b0001, // PROTOCOL_VERSION
                messageTypeSpecificFlags = 0b0000, // NO_SEQUENCE
                serialMethod = 0b0001, // JSON
                compressionType = 0b0000, // NO_COMPRESSION
                reservedData = 0x00,
                extensionHeader = new ArrayBuffer(0)
            ) {
                const buffer = new ArrayBuffer(8);
                const dataView = new DataView(buffer);
                const headerSize = Math.trunc(extensionHeader.byteLength / 4) + 1;

                dataView.setUint8(0, (version << 4) | headerSize);
                dataView.setUint8(1, (messageType << 4) | messageTypeSpecificFlags);
                dataView.setUint8(2, (serialMethod << 4) | compressionType);
                dataView.setUint8(3, reservedData);

                return buffer;
            }

            generateUUID() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                    const r = Math.random() * 16 | 0;
                    const v = c == 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                });
            }
        }

        // 初始化应用
        let asrClient;
        
        document.addEventListener('DOMContentLoaded', () => {
            asrClient = new MicrophoneASRWebClient();
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (asrClient && asrClient.isRecording) {
                asrClient.stopRecording();
            }
        });
    </script>
</body>
</html>
